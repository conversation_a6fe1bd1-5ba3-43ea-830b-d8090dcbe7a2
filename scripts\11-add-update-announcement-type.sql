-- 添加 'update' 类型到公告类型约束中
-- 删除旧的约束并创建新的约束

-- 1. 删除现有的type字段约束
ALTER TABLE announcements DROP CONSTRAINT IF EXISTS announcements_type_check;

-- 2. 添加新的约束，包含 'update' 类型
ALTER TABLE announcements 
ADD CONSTRAINT announcements_type_check 
CHECK (type IN ('info', 'warning', 'success', 'error', 'update'));

-- 3. 更新注释以反映新的类型
COMMENT ON COLUMN announcements.type IS '公告类型：info-信息，warning-警告，success-成功，error-错误，update-更新';

-- 4. 插入一个示例更新类型公告
INSERT INTO announcements (title, content, type, priority, target_audience) VALUES
('系统功能更新', '我们新增了公告管理功能，现在可以创建和管理不同类型的系统通知。', 'update', 85, 'all');

-- 确认修改成功
SELECT 
    table_name, 
    column_name, 
    check_clause 
FROM information_schema.check_constraints cc
JOIN information_schema.constraint_column_usage ccu 
ON cc.constraint_name = ccu.constraint_name
WHERE ccu.table_name = 'announcements' 
AND ccu.column_name = 'type';