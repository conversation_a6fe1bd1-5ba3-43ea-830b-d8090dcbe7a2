"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"

export default function DebugAuth() {
  const [result, setResult] = useState("")

  const testLogin = async () => {
    setResult("测试开始...")
    
    try {
      // 1. 测试登录API
      const loginResponse = await fetch("/api/admin/auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ password: "admin123" })
      })
      
      const loginData = await loginResponse.json()
      console.log("登录响应:", loginData)
      
      if (loginData.success) {
        // 2. 保存token
        localStorage.setItem("admin_token", loginData.token)
        
        // 3. 测试验证API
        const verifyResponse = await fetch("/api/admin/auth", {
          headers: {
            "Authorization": `Bearer ${loginData.token}`,
            "x-admin-token": loginData.token
          }
        })
        
        const verifyData = await verifyResponse.json()
        console.log("验证响应:", verifyData)
        
        setResult(`
登录成功: ${loginData.success}
Token: ${loginData.token?.substring(0, 20)}...
验证成功: ${verifyData.success}
Cookies: ${document.cookie}
        `)
        
        // 4. 测试直接跳转
        setTimeout(() => {
          console.log("尝试跳转...")
          window.location.href = "/admin"
        }, 2000)
        
      } else {
        setResult(`登录失败: ${loginData.error}`)
      }
      
    } catch (error) {
      console.error("测试错误:", error)
      setResult(`测试异常: ${error}`)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">认证调试页面</h1>
      <Button onClick={testLogin} className="mb-4">
        测试登录流程
      </Button>
      <pre className="bg-gray-100 p-4 rounded whitespace-pre-wrap">
        {result}
      </pre>
    </div>
  )
}