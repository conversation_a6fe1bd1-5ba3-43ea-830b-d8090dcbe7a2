#!/usr/bin/env node

/**
 * 编辑器语法检查脚本
 */

const { spawn } = require('child_process');

console.log('🔍 检查 TipTap 编辑器语法...');

// 运行 TypeScript 编译检查
const tsc = spawn('npx', ['tsc', '--noEmit', '--project', '.'], {
  cwd: process.cwd(),
  stdio: 'pipe'
});

let output = '';
let errorOutput = '';

tsc.stdout.on('data', (data) => {
  output += data.toString();
});

tsc.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

tsc.on('close', (code) => {
  console.log('\n📋 TypeScript 编译检查结果:');
  console.log('=====================================');
  
  if (code === 0) {
    console.log('✅ 编辑器语法检查通过！');
    console.log('📝 所有类型定义正确');
    console.log('🎯 可以安全启动开发服务器');
  } else {
    console.log('❌ 发现语法错误:');
    console.log(errorOutput);
    if (output) {
      console.log('输出信息:');
      console.log(output);
    }
  }
  
  console.log('\n🚀 下一步操作建议:');
  console.log('=====================================');
  if (code === 0) {
    console.log('1. 运行 pnpm dev 启动开发服务器');
    console.log('2. 访问管理后台测试编辑器功能');
    console.log('3. 验证以下核心功能:');
    console.log('   • 编辑区域扩展和行间距');
    console.log('   • H1-H3 标题设置');
    console.log('   • 代码语法高亮');
    console.log('   • 图片上传优化');
    console.log('   • Markdown 导入导出');
    console.log('   • 字符统计显示');
  } else {
    console.log('1. 修复上述语法错误');
    console.log('2. 重新运行此检查脚本');
    console.log('3. 确保所有导入和类型定义正确');
  }
});