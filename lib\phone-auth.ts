/**
 * 手机验证码认证系统
 * 特点：快速便捷、国内用户熟悉、安全可靠
 */

import crypto from 'crypto'
import { supabaseAdmin } from './supabase'

/**
 * 生成手机验证码
 * @param phone 手机号码
 * @param type 验证码类型
 * @returns 验证码和过期时间
 */
export async function generatePhoneCode(
  phone: string, 
  type: 'login' | 'register' | 'reset' = 'login'
): Promise<{
  code: string
  expiresAt: Date
}> {
  try {
    // 生成6位数字验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString()
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000) // 5分钟有效期
    
    console.log('📱 生成手机验证码:', { phone, code, type })
    
    // 保存到数据库
    await supabaseAdmin
      .from('phone_codes')
      .insert({
        phone,
        code,
        type,
        expires_at: expiresAt.toISOString(),
        used: false,
        attempts: 0,
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 验证码生成成功:', { phone, type })
    
    return {
      code,
      expiresAt
    }
  } catch (error) {
    console.error('❌ 验证码生成失败:', error)
    throw new Error('生成验证码失败')
  }
}

/**
 * 验证手机验证码
 * @param phone 手机号码
 * @param code 验证码
 * @param type 验证码类型
 * @returns 验证结果和用户信息
 */
export async function verifyPhoneCode(
  phone: string,
  code: string,
  type: 'login' | 'register' | 'reset' = 'login'
): Promise<{
  valid: boolean
  user?: any
  error?: string
}> {
  try {
    console.log('🔍 开始验证手机验证码:', { phone, code, type })
    
    // 从数据库获取验证码信息
    const { data: codeData, error: fetchError } = await supabaseAdmin
      .from('phone_codes')
      .select('*')
      .eq('phone', phone)
      .eq('code', code)
      .eq('type', type)
      .eq('used', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    if (fetchError || !codeData) {
      console.log('❌ 验证码不存在或已使用')
      
      // 增加尝试次数
      await supabaseAdmin
        .from('phone_codes')
        .update({ attempts: (codeData?.attempts || 0) + 1 })
        .eq('phone', phone)
        .eq('type', type)
        .eq('used', false)
      
      return { valid: false, error: '验证码错误或已过期' }
    }
    
    // 检查是否过期
    const expiresAt = new Date(codeData.expires_at)
    if (expiresAt < new Date()) {
      console.log('❌ 验证码已过期')
      return { valid: false, error: '验证码已过期，请重新获取' }
    }
    
    // 检查尝试次数
    if (codeData.attempts >= 3) {
      console.log('❌ 验证码尝试次数过多')
      return { valid: false, error: '验证码尝试次数过多，请重新获取' }
    }
    
    // 标记验证码为已使用
    await supabaseAdmin
      .from('phone_codes')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('id', codeData.id)
    
    // 获取或创建用户
    let user = await getOrCreateUserByPhone(phone)
    
    console.log('✅ 手机验证码验证成功:', { phone, userId: user.id })
    
    return {
      valid: true,
      user
    }
  } catch (error) {
    console.error('❌ 手机验证码验证异常:', error)
    return { valid: false, error: '验证过程出现错误' }
  }
}

/**
 * 获取或创建用户（通过手机号）
 * @param phone 手机号码
 * @returns 用户信息
 */
async function getOrCreateUserByPhone(phone: string) {
  try {
    // 先尝试获取现有用户
    const { data: existingUser, error: fetchError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('phone', phone)
      .single()
    
    if (existingUser && !fetchError) {
      console.log('📍 找到现有用户:', { phone, userId: existingUser.id })
      
      // 更新最后登录时间
      await supabaseAdmin
        .from('users')
        .update({ 
          last_login_at: new Date().toISOString(),
          login_count: (existingUser.login_count || 0) + 1,
          phone_verified: true
        })
        .eq('id', existingUser.id)
      
      return existingUser
    }
    
    // 创建新用户
    const userId = crypto.randomUUID()
    const newUser = {
      id: userId,
      phone,
      username: `用户${phone.slice(-4)}`, // 使用手机号后4位作为用户名
      nickname: `手机用户${phone.slice(-4)}`,
      auth_type: 'phone',
      phone_verified: true,
      status: 'active',
      login_count: 1,
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    }
    
    const { data: createdUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert(newUser)
      .select()
      .single()
    
    if (createError) {
      console.error('❌ 用户创建失败:', createError)
      throw new Error('用户创建失败')
    }
    
    console.log('✅ 新用户创建成功:', { phone, userId })
    
    return createdUser
  } catch (error) {
    console.error('❌ 获取或创建用户失败:', error)
    throw error
  }
}

/**
 * 发送短信验证码
 * @param phone 手机号码
 * @param code 验证码
 * @param type 验证码类型
 * @returns 发送结果
 */
export async function sendSMSCode(
  phone: string,
  code: string,
  type: 'login' | 'register' | 'reset' = 'login'
): Promise<boolean> {
  try {
    console.log('📲 准备发送短信验证码:', { phone, code, type })
    
    // 这里可以集成短信服务商，如：
    // - 腾讯云短信
    // - 阿里云短信
    // - 华为云短信
    // - 网易云信
    
    // 演示模式：记录到控制台
    const typeMap = {
      login: '登录',
      register: '注册', 
      reset: '重置密码'
    }
    
    console.log(`
╭─────────────────────────────────────╮
│  📱 知识商城 - 手机验证码短信        │
├─────────────────────────────────────┤
│  手机号: ${phone.padEnd(20)}       │
│  验证码: ${code}                      │
│  用途: ${typeMap[type]}                       │
│  有效期: 5分钟                       │
│  【知识商城】您的${typeMap[type]}验证码是${code}，│
│  5分钟内有效，请勿泄露给他人。        │
╰─────────────────────────────────────╯
    `)
    
    // 实际实现中替换为真实的短信发送
    // const result = await smsService.send({
    //   phone,
    //   template: 'SMS_TEMPLATE_ID',
    //   params: { code, type: typeMap[type] }
    // })
    
    // 模拟发送成功
    return true
  } catch (error) {
    console.error('❌ 短信验证码发送失败:', error)
    return false
  }
}

/**
 * 验证手机号格式
 * @param phone 手机号码
 * @returns 是否有效
 */
export function validatePhone(phone: string): boolean {
  // 中国大陆手机号格式：1开头，第二位是3-9，总共11位数字
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 清理过期的验证码
 * 建议设置定时任务调用此函数
 */
export async function cleanupExpiredPhoneCodes(): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .from('phone_codes')
      .delete()
      .lt('expires_at', new Date().toISOString())
    
    if (error) {
      console.error('❌ 清理过期验证码失败:', error)
      return 0
    }
    
    const deletedCount = Array.isArray(data) ? data.length : 0
    console.log(`🧹 清理了 ${deletedCount} 个过期的验证码`)
    
    return deletedCount
  } catch (error) {
    console.error('❌ 清理过期验证码异常:', error)
    return 0
  }
}

/**
 * 检查手机号发送频率限制
 * @param phone 手机号码
 * @param timeWindow 时间窗口（毫秒）
 * @param maxAttempts 最大尝试次数
 * @returns 是否允许发送
 */
export async function checkPhoneRateLimit(
  phone: string,
  timeWindow: number = 3600000, // 1小时
  maxAttempts: number = 5
): Promise<boolean> {
  try {
    const windowStart = new Date(Date.now() - timeWindow).toISOString()
    
    const { data: attempts, error } = await supabaseAdmin
      .from('phone_codes')
      .select('id')
      .eq('phone', phone)
      .gte('created_at', windowStart)
    
    if (error) {
      console.error('❌ 手机号频率限制检查失败:', error)
      return false
    }
    
    const attemptCount = attempts?.length || 0
    
    if (attemptCount >= maxAttempts) {
      console.log(`⚠️ 手机号 ${phone} 超出频率限制: ${attemptCount}/${maxAttempts}`)
      return false
    }
    
    return true
  } catch (error) {
    console.error('❌ 手机号频率限制检查异常:', error)
    return false
  }
}

/**
 * 配置常量
 */
export const PHONE_AUTH_CONFIG = {
  CODE_LENGTH: 6, // 验证码长度
  CODE_EXPIRES_MINUTES: 5, // 验证码有效期(分钟)
  MAX_ATTEMPTS: 3, // 最大尝试次数
  MAX_REQUESTS_PER_HOUR: 5, // 每小时最大请求次数
  CLEANUP_INTERVAL_HOURS: 6 // 清理间隔(小时)
} as const