'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { NavigationHint } from '@/components/ui/navigation-hint'
import { useRouter } from 'next/navigation'
import { 
  CheckCircle, 
  AlertTriangle,
  Eye,
  MousePointer,
  Keyboard,
  Monitor,
  Smartphone,
  ExternalLink
} from 'lucide-react'

export default function NavigationTestPage() {
  const router = useRouter()
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})

  const markTest = (testId: string, passed: boolean) => {
    setTestResults(prev => ({ ...prev, [testId]: passed }))
  }

  // 模拟教程预览函数
  const handleTestPreview = (testId: string, e?: React.MouseEvent) => {
    const url = `/tutorial/1`
    
    if (e && (e.ctrlKey || e.metaKey || e.button === 1)) {
      window.open(url, '_blank')
      markTest(testId, true)
    } else {
      router.push(url)
      markTest(testId, true)
    }
  }

  const testCases = [
    {
      id: 'normal-click',
      title: '普通点击测试',
      description: '点击应该在当前页面跳转',
      icon: MousePointer,
      status: testResults['normal-click']
    },
    {
      id: 'ctrl-click',
      title: 'Ctrl+点击测试',  
      description: '按住Ctrl键点击应该开新窗口',
      icon: Keyboard,
      status: testResults['ctrl-click']
    },
    {
      id: 'middle-click',
      title: '中键点击测试',
      description: '鼠标中键点击应该开新窗口',
      icon: Monitor,
      status: testResults['middle-click']
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <ExternalLink className="h-6 w-6" />
              导航优化验证页面
            </h1>
            <p className="mt-2 opacity-90">测试新的智能导航功能</p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* 导航提示 */}
            <NavigationHint />
            
            {/* 优化概览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  优化效果概览
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">70-80%</div>
                    <div className="text-sm text-green-700">预计减少不必要的新标签页</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">60%</div>
                    <div className="text-sm text-blue-700">减少标签页切换时间</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">100%</div>
                    <div className="text-sm text-purple-700">保持现有功能完整性</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 测试用例 */}
            <Card>
              <CardHeader>
                <CardTitle>交互测试用例</CardTitle>
                <CardDescription>
                  请按照说明测试不同的点击方式，验证导航行为是否符合预期
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {testCases.map((test) => {
                  const Icon = test.icon
                  return (
                    <div key={test.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Icon className="h-5 w-5 text-gray-600" />
                        <div>
                          <h3 className="font-medium">{test.title}</h3>
                          <p className="text-sm text-gray-600">{test.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        {test.status !== undefined && (
                          <Badge variant={test.status ? "default" : "secondary"}>
                            {test.status ? "通过" : "待测试"}
                          </Badge>
                        )}
                        <Button
                          onClick={(e) => handleTestPreview(test.id, e)}
                          onMouseDown={(e) => e.button === 1 && handleTestPreview(test.id, e)}
                          variant="outline"
                          size="sm"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          测试预览
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* 优化前后对比 */}
            <Card>
              <CardHeader>
                <CardTitle>优化前后对比</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-red-600 mb-3 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      优化前
                    </h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <span className="text-red-500">❌</span>
                        <span>每次查看教程都会打开新标签页</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500">❌</span>
                        <span>管理员预览多个教程产生大量标签页</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500">❌</span>
                        <span>图片预览也会开新窗口</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500">❌</span>
                        <span>用户无法控制页面打开方式</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium text-green-600 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      优化后
                    </h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <span className="text-green-500">✅</span>
                        <span>普通点击在当前页面跳转</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500">✅</span>
                        <span>支持Ctrl+点击开新窗口</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500">✅</span>
                        <span>图片使用模态框预览</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500">✅</span>
                        <span>符合Web标准的用户交互习惯</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 使用场景 */}
            <Card>
              <CardHeader>
                <CardTitle>典型使用场景</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-3 bg-gray-50 rounded">
                    <strong>场景1 - 普通用户浏览：</strong> 
                    <span className="text-gray-700 ml-2">
                      首页 → 点击教程 → 阅读完毕 → 浏览器返回 → 继续浏览
                    </span>
                  </div>
                  <div className="p-3 bg-gray-50 rounded">
                    <strong>场景2 - 管理员预览：</strong> 
                    <span className="text-gray-700 ml-2">
                      管理后台 → 点击预览 → 检查内容 → 返回继续管理
                    </span>
                  </div>
                  <div className="p-3 bg-gray-50 rounded">
                    <strong>场景3 - 对比多个教程：</strong> 
                    <span className="text-gray-700 ml-2">
                      Ctrl+点击多个教程 → 在不同标签页中对比内容
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 返回按钮 */}
            <div className="flex justify-center pt-4">
              <Button 
                onClick={() => router.push('/')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                返回首页测试实际效果
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}