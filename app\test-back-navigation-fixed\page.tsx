'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Home, Library, Shield, CheckCircle } from 'lucide-react'

export default function TestBackNavigationFixedPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <CheckCircle className="h-6 w-6" />
              返回按钮修复测试
            </h1>
            <p className="mt-2 opacity-90">测试从不同页面进入教程后的返回行为</p>
          </div>
          
          <div className="p-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>修复内容</CardTitle>
                <CardDescription>
                  智能返回功能现在可以正确识别用户来源页面
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-800 mb-2">✅ 已修复的问题</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• 从主页进入教程 → 返回按钮正确返回主页</li>
                    <li>• 从"我的教程"进入 → 返回按钮正确返回"我的教程"页面</li>
                    <li>• 从管理后台进入 → 返回按钮正确返回管理后台</li>
                    <li>• 添加了调试日志以便问题排查</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>返回逻辑说明</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <Home className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-blue-800">主页来源</div>
                      <div className="text-sm text-blue-700">
                        当 document.referrer 是网站根路径时，返回按钮导向主页 "/"
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                    <Library className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-purple-800">我的教程来源</div>
                      <div className="text-sm text-purple-700">
                        当 document.referrer 包含 "/my-tutorials" 时，返回 "/my-tutorials"
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                    <Shield className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-orange-800">管理后台来源</div>
                      <div className="text-sm text-orange-700">
                        当 document.referrer 包含 "/admin" 时，返回 "/admin"
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>测试步骤</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border border-slate-200 rounded-lg">
                    <h4 className="font-medium mb-2">1. 测试主页返回</h4>
                    <p className="text-sm text-slate-600 mb-3">
                      从主页点击"继续学习"或"重新阅读"按钮，然后在教程页面点击返回按钮
                    </p>
                    <Button 
                      onClick={() => router.push('/')}
                      variant="outline" 
                      size="sm"
                    >
                      <Home className="h-4 w-4 mr-2" />
                      前往主页测试
                    </Button>
                  </div>
                  
                  <div className="p-4 border border-slate-200 rounded-lg">
                    <h4 className="font-medium mb-2">2. 测试我的教程返回</h4>
                    <p className="text-sm text-slate-600 mb-3">
                      从"我的教程"页面点击教程，然后在教程页面点击返回按钮
                    </p>
                    <Button 
                      onClick={() => router.push('/my-tutorials')}
                      variant="outline" 
                      size="sm"
                    >
                      <Library className="h-4 w-4 mr-2" />
                      前往我的教程测试
                    </Button>
                  </div>
                  
                  <div className="p-4 border border-slate-200 rounded-lg">
                    <h4 className="font-medium mb-2">3. 测试管理后台返回</h4>
                    <p className="text-sm text-slate-600 mb-3">
                      从管理后台预览教程，然后在教程页面点击返回按钮
                    </p>
                    <Button 
                      onClick={() => router.push('/admin')}
                      variant="outline" 
                      size="sm"
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      前往管理后台测试
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="flex justify-center pt-4">
              <Button 
                onClick={() => router.push('/')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4" />
                返回主页
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}