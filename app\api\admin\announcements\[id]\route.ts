import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: {
    id: string
  }
}

// 获取单个公告
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const announcementId = parseInt(params.id)
    
    if (!announcementId) {
      return NextResponse.json({ error: "无效的公告ID" }, { status: 400 })
    }

    const { data: announcement, error } = await supabaseAdmin
      .from('announcements')
      .select('*')
      .eq('id', announcementId)
      .single()

    if (error || !announcement) {
      return NextResponse.json({ error: "公告不存在" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: announcement
    })

  } catch (error) {
    console.error("获取公告详情异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}

// 更新公告
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const announcementId = parseInt(params.id)
    
    if (!announcementId) {
      return NextResponse.json({ error: "无效的公告ID" }, { status: 400 })
    }

    const {
      title,
      content,
      type,
      priority,
      is_active,
      start_date,
      end_date,
      target_audience
    } = await request.json()

    // 验证必填字段
    if (!title || !content) {
      return NextResponse.json(
        { error: "标题和内容是必填字段" }, 
        { status: 400 }
      )
    }

    // 验证枚举值
    const validTypes = ['info', 'warning', 'success', 'error', 'update']
    const validAudiences = ['all', 'users', 'admins']
    
    if (type && !validTypes.includes(type)) {
      return NextResponse.json(
        { error: "无效的公告类型" }, 
        { status: 400 }
      )
    }

    if (target_audience && !validAudiences.includes(target_audience)) {
      return NextResponse.json(
        { error: "无效的目标受众" }, 
        { status: 400 }
      )
    }

    const { data: announcement, error } = await supabaseAdmin
      .from('announcements')
      .update({
        title: title.trim(),
        content: content.trim(),
        type,
        priority: Math.max(0, Math.min(100, parseInt(priority))),
        is_active,
        start_date,
        end_date: end_date || null,
        target_audience,
        updated_at: new Date().toISOString()
      })
      .eq('id', announcementId)
      .select()
      .single()

    if (error) {
      console.error("更新公告错误:", error)
      return NextResponse.json({ error: "更新公告失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: announcement,
      message: "公告更新成功"
    })

  } catch (error) {
    console.error("更新公告异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}

// 删除公告
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const announcementId = parseInt(params.id)
    
    if (!announcementId) {
      return NextResponse.json({ error: "无效的公告ID" }, { status: 400 })
    }

    // 检查公告是否存在
    const { data: announcement, error: checkError } = await supabaseAdmin
      .from('announcements')
      .select('title')
      .eq('id', announcementId)
      .single()

    if (checkError || !announcement) {
      return NextResponse.json({ error: "公告不存在" }, { status: 404 })
    }

    // 删除公告（会级联删除已读记录）
    const { error: deleteError } = await supabaseAdmin
      .from('announcements')
      .delete()
      .eq('id', announcementId)

    if (deleteError) {
      console.error("删除公告错误:", deleteError)
      return NextResponse.json({ error: "删除公告失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `公告"${announcement.title}"已删除`
    })

  } catch (error) {
    console.error("删除公告异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}