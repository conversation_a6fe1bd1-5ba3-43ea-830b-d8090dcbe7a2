'use client'

import { useState, useCallback, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  Image as ImageIcon, 
  Video, 
  FileText, 
  Trash2, 
  Copy,
  Eye,
  Download
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

// ==========================================
// 媒体管理组件 - 支持上传、预览、管理
// ==========================================

interface MediaFile {
  id: number
  file_name: string
  original_name: string
  file_type: 'image' | 'video' | 'document'
  file_size: number
  public_url: string
  thumbnail_url?: string
  created_at: string
}

interface MediaManagerProps {
  tutorialId: number
  onMediaSelect?: (media: MediaFile) => void
  allowedTypes?: string[]
  maxFileSize?: number
  className?: string
}

export function MediaManager({
  tutorialId,
  onMediaSelect,
  allowedTypes = ['image', 'video', 'document'],
  maxFileSize = 50 * 1024 * 1024, // 50MB
  className = ''
}: MediaManagerProps) {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragActive, setDragActive] = useState(false)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // 智能媒体预览：图片用模态框，其他文件用新窗口
  const handlePreviewMedia = (file: MediaFile, e?: React.MouseEvent) => {
    // 对于图片文件，使用模态框预览更好
    if (file.file_type === 'image') {
      setPreviewImage(file.public_url)
    } else {
      // 非图片文件：检测用户意图
      if (e && (e.ctrlKey || e.metaKey || e.button === 1)) {
        window.open(file.public_url, '_blank')
      } else {
        // 直接在当前页面打开可能不合适，保持新窗口但给用户提示
        window.open(file.public_url, '_blank')
      }
    }
  }

  // 获取媒体文件列表
  const fetchMediaFiles = useCallback(async () => {
    try {
      const typeParam = selectedType !== 'all' ? `&type=${selectedType}` : ''
      const response = await fetch(`/api/admin/media?tutorialId=${tutorialId}${typeParam}`)
      const result = await response.json()
      
      if (result.success) {
        setMediaFiles(result.data)
      }
    } catch (error) {
      console.error('获取媒体文件失败:', error)
    }
  }, [tutorialId, selectedType])

  // 上传文件
  const uploadFile = useCallback(async (file: File) => {
    setUploading(true)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('tutorialId', tutorialId.toString())

      const response = await fetch('/api/admin/media', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "上传成功",
          description: `文件 ${file.name} 已成功上传`,
        })
        
        // 刷新文件列表
        await fetchMediaFiles()
        
        // 如果有选择回调，自动选择新上传的文件
        if (onMediaSelect && result.data) {
          onMediaSelect(result.data)
        }
      } else {
        toast({
          title: "上传失败",
          description: result.error,
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "上传错误",
        description: "文件上传过程中发生错误",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }, [tutorialId, fetchMediaFiles, onMediaSelect, toast])

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList) => {
    Array.from(files).forEach(file => {
      // 验证文件大小
      if (file.size > maxFileSize) {
        toast({
          title: "文件过大",
          description: `文件 ${file.name} 超过 ${maxFileSize / 1024 / 1024}MB 限制`,
          variant: "destructive"
        })
        return
      }

      uploadFile(file)
    })
  }, [uploadFile, maxFileSize, toast])

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files)
    }
  }, [handleFileSelect])

  // 复制URL到剪贴板
  const copyToClipboard = useCallback(async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      toast({
        title: "复制成功",
        description: "文件URL已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive"
      })
    }
  }, [toast])

  // 获取文件类型图标
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <ImageIcon className="h-4 w-4" />
      case 'video':
        return <Video className="h-4 w-4" />
      case 'document':
        return <FileText className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            文件上传
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {uploading ? (
              <div className="space-y-2">
                <div className="text-blue-600">上传中...</div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            ) : (
              <>
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">
                  拖拽文件到此处，或点击选择文件
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  支持图片、视频、文档，最大 {maxFileSize / 1024 / 1024}MB
                </p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                >
                  选择文件
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                  accept=".jpg,.jpeg,.png,.gif,.webp,.mp4,.webm,.pdf,.txt,.md"
                />
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 文件筛选 */}
      <div className="flex gap-2">
        <Button
          variant={selectedType === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('all')}
        >
          全部
        </Button>
        <Button
          variant={selectedType === 'image' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('image')}
        >
          图片
        </Button>
        <Button
          variant={selectedType === 'video' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('video')}
        >
          视频
        </Button>
        <Button
          variant={selectedType === 'document' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedType('document')}
        >
          文档
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchMediaFiles}
        >
          刷新
        </Button>
      </div>

      {/* 文件列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {mediaFiles.map((file) => (
          <Card key={file.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-100 flex items-center justify-center">
              {file.file_type === 'image' ? (
                <img
                  src={file.public_url}
                  alt={file.original_name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-center">
                  {getFileIcon(file.file_type)}
                  <p className="text-sm text-gray-500 mt-2">{file.file_type}</p>
                </div>
              )}
            </div>
            
            <CardContent className="p-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium truncate" title={file.original_name}>
                    {file.original_name}
                  </h4>
                  <Badge variant="secondary">
                    {file.file_type}
                  </Badge>
                </div>
                
                <p className="text-xs text-gray-500">
                  {formatFileSize(file.file_size)}
                </p>
                
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(file.public_url)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => handlePreviewMedia(file, e)}
                    onMouseDown={(e) => e.button === 1 && handlePreviewMedia(file, e)}
                    title={file.file_type === 'image' ? '查看图片' : '在新窗口中打开'}
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                  
                  {onMediaSelect && (
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => onMediaSelect(file)}
                    >
                      选择
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {mediaFiles.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p>暂无媒体文件</p>
          <p className="text-sm">上传一些文件开始使用</p>
        </div>
      )}

      {/* 图片预览模态框 */}
      {previewImage && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setPreviewImage(null)}
        >
          <div 
            className="relative max-w-4xl max-h-[90vh] m-4"
            onClick={(e) => e.stopPropagation()}
          >
            <img 
              src={previewImage} 
              alt="预览图片" 
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            />
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 bg-white hover:bg-gray-100"
              onClick={() => setPreviewImage(null)}
            >
              ✕
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}