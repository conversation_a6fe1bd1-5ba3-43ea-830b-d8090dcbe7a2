"use client"

import { useState, useEffect } from "react"
import { Bell, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"

interface Announcement {
  id: number
  title: string
  content: string
  type: 'info' | 'warning' | 'success' | 'error' | 'update'
  priority: number
  start_date: string
  end_date: string | null
  target_audience: 'all' | 'users' | 'admins'
  created_at: string
  is_read: boolean
}

interface AnnouncementData {
  announcements: Announcement[]
  unread_count: number
}

export function AnnouncementBell() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  // 加载公告数据
  const loadAnnouncements = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/announcements?limit=20')
      if (response.ok) {
        const data: { success: boolean; data: AnnouncementData } = await response.json()
        if (data.success) {
          setAnnouncements(data.data.announcements)
          setUnreadCount(data.data.unread_count)
        }
      }
    } catch (error) {
      console.error('加载公告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 标记单个公告为已读
  const markAsRead = async (announcementId: number) => {
    try {
      const response = await fetch('/api/announcements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ announcement_id: announcementId })
      })

      if (response.ok) {
        // 更新本地状态
        setAnnouncements(prev => 
          prev.map(announcement => 
            announcement.id === announcementId 
              ? { ...announcement, is_read: true }
              : announcement
          )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }

  // 标记所有公告为已读
  const markAllAsRead = async () => {
    try {
      // 可以在这里添加批量标记已读的API调用
      // 目前先使用本地状态更新
      setAnnouncements(prev => 
        prev.map(announcement => ({ ...announcement, is_read: true }))
      )
      setUnreadCount(0)
    } catch (error) {
      console.error('标记全部已读失败:', error)
    }
  }

  // 清除全部消息
  const clearAllMessages = async () => {
    try {
      // 先标记所有消息为已读
      await markAllAsRead()
      
      // 清空本地状态
      setAnnouncements([])
      setUnreadCount(0)
      
      // 关闭弹窗
      setIsOpen(false)
    } catch (error) {
      console.error('清除消息失败:', error)
    }
  }

  // 获取公告类型对应的样式
  const getTypeStyle = (type: string) => {
    switch (type) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'update':
        return 'bg-purple-50 border-purple-200 text-purple-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'error': return '错误'
      case 'warning': return '警告'
      case 'success': return '成功'
      case 'update': return '更新'
      default: return '信息'
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadAnnouncements()
  }, [])

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">系统公告</h3>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={markAllAsRead}
                  className="text-xs text-blue-600 hover:text-blue-700"
                >
                  全部已读
                </Button>
              )}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearAllMessages}
                className="text-xs text-red-600 hover:text-red-700"
                title="清除所有消息"
              >
                清除全部
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          {unreadCount > 0 && (
            <p className="text-sm text-gray-600 mt-1">
              您有 {unreadCount} 条未读公告
            </p>
          )}
        </div>

        <ScrollArea className="h-80">
          {loading ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-pulse">加载中...</div>
            </div>
          ) : announcements.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>暂无公告</p>
            </div>
          ) : (
            <div className="p-2 space-y-2">
              {announcements.map((announcement) => (
                <div
                  key={announcement.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                    announcement.is_read 
                      ? 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100' 
                      : getTypeStyle(announcement.type) + ' hover:opacity-90'
                  }`}
                  onClick={() => !announcement.is_read && markAsRead(announcement.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge 
                          variant={announcement.is_read ? "secondary" : "default"}
                          className="text-xs flex-shrink-0"
                        >
                          {getTypeLabel(announcement.type)}
                        </Badge>
                        {!announcement.is_read && (
                          <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                        )}
                        <span className="text-xs text-gray-500 flex-shrink-0">
                          {new Date(announcement.created_at).toLocaleDateString('zh-CN', {
                            month: 'numeric',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      <h4 className="font-medium text-sm mb-1 line-clamp-1">
                        {announcement.title}
                      </h4>
                      <p className="text-xs line-clamp-3 opacity-90 leading-relaxed">
                        {announcement.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* 底部提示 */}
              {announcements.length >= 20 && (
                <div className="text-center py-2 text-xs text-gray-400 border-t border-gray-100 mt-2">
                  显示最新 {announcements.length} 条公告
                </div>
              )}
            </div>
          )}
        </ScrollArea>

      </PopoverContent>
    </Popover>
  )
}