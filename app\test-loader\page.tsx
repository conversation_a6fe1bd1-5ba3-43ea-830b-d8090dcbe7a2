'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AnimatedLoader } from '@/components/ui/animated-loader'
import { useRouter } from 'next/navigation'
import { 
  <PERSON>rk<PERSON>, 
  Palette,
  Eye,
  Play,
  ArrowLeft,
  Settings,
  Lock
} from 'lucide-react'

export default function LoaderShowcasePage() {
  const router = useRouter()
  const [showDemo, setShowDemo] = useState(false)

  const handleAdminDemo = () => {
    setShowDemo(true)
    // 模拟加载时间
    setTimeout(() => {
      setShowDemo(false)
    }, 3000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Sparkles className="h-6 w-6" />
              新版等待动画展示
            </h1>
            <p className="mt-2 opacity-90">精美的"YOU"文字动画替换旧的转圈加载</p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* 对比展示 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5 text-purple-600" />
                  新旧动画对比
                </CardTitle>
                <CardDescription>
                  左侧为旧版转圈动画，右侧为新版"YOU"文字动画
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* 旧版动画 */}
                  <div className="text-center p-8 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">旧版加载动画</h3>
                    <div className="flex justify-center mb-4">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-900"></div>
                    </div>
                    <p className="text-sm text-gray-600">简单的转圈动画</p>
                  </div>
                  
                  {/* 新版动画 */}
                  <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">新版"YOU"动画</h3>
                    <div className="flex justify-center mb-4">
                      <AnimatedLoader size="md" />
                    </div>
                    <p className="text-sm text-gray-600">精美的渐变文字动画</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 尺寸展示 */}
            <Card>
              <CardHeader>
                <CardTitle>不同尺寸展示</CardTitle>
                <CardDescription>
                  新动画支持三种尺寸：小型(sm)、中型(md)、大型(lg)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">小型 (sm)</h4>
                    <div className="flex justify-center mb-2">
                      <AnimatedLoader size="sm" />
                    </div>
                    <p className="text-xs text-gray-600">适用于按钮内加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">中型 (md)</h4>
                    <div className="flex justify-center mb-2">
                      <AnimatedLoader size="md" />
                    </div>
                    <p className="text-xs text-gray-600">适用于页面加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">大型 (lg)</h4>
                    <div className="flex justify-center mb-2">
                      <AnimatedLoader size="lg" />
                    </div>
                    <p className="text-xs text-gray-600">适用于全屏加载</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 实际应用场景 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-600" />
                  实际应用场景
                </CardTitle>
                <CardDescription>
                  在实际使用场景中查看新动画效果
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 按钮中的加载动画 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">按钮加载状态</h4>
                  <div className="flex gap-3">
                    <Button disabled className="flex items-center gap-2">
                      <AnimatedLoader size="sm" />
                      验证中...
                    </Button>
                    <Button variant="outline" disabled className="flex items-center gap-2">
                      <AnimatedLoader size="sm" />
                      处理中...
                    </Button>
                  </div>
                </div>

                {/* 管理后台加载演示 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">管理后台加载演示</h4>
                  <Button onClick={handleAdminDemo} className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    演示管理后台加载
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 技术特性 */}
            <Card>
              <CardHeader>
                <CardTitle>技术特性</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">动画效果</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 流畅的SVG路径动画</li>
                      <li>• 多彩渐变色彩系统</li>
                      <li>• 复合动画效果组合</li>
                      <li>• 自适应循环播放</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">技术实现</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• CSS @keyframes 动画</li>
                      <li>• SVG 渐变和路径</li>
                      <li>• TypeScript 类型安全</li>
                      <li>• Tailwind CSS 响应式</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 应用位置 */}
            <Card>
              <CardHeader>
                <CardTitle>已应用位置</CardTitle>
                <CardDescription>
                  新动画已在以下位置替换了旧的转圈动画
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Settings className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium">管理后台加载页面</div>
                      <div className="text-sm text-gray-600">用户进入管理后台时的身份验证加载</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Lock className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium">管理员登录按钮</div>
                      <div className="text-sm text-gray-600">点击登录时的验证加载状态</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 返回按钮 */}
            <div className="flex justify-center pt-4">
              <Button 
                onClick={() => router.push('/admin')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4" />
                返回管理后台
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 全屏演示模态框 */}
      {showDemo && (
        <div className="fixed inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-auto shadow-lg border-slate-200">
            <CardContent className="flex flex-col items-center py-12">
              <AnimatedLoader size="lg" className="mb-6" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">正在验证身份</h3>
              <p className="text-slate-500 text-center">请稍候...</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}