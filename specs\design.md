# 技术设计规格 - 知识商城

*最后更新：2025年1月29日*
*版本：v2.1 - 管理员预览系统 + 学习成就优化版本*

## 系统架构设计（最新架构）

### 整体架构
```
                    用户界面层 (Next.js + React)
                            |
                    智能学习跟踪层 (新增核心层)
                            |
                      API 网关层 (Next.js API Routes)
                            |
                 +----------+----------+
                 |                     |
           业务逻辑层                数据访问层
        (TypeScript 函数)      (Supabase Client)
                 |                     |
                 +----------+----------+
                            |
                    数据存储层 (PostgreSQL)
                            |
                    学习数据缓存层 (localStorage + 云端同步)
```

### 技术栈架构（2025年最新版本）
- **前端框架**：Next.js 14.2.30 (App Router) + TypeScript 5.x
- **状态管理**：React Hooks + 高性能学习进度管理
- **UI组件**：shadcn/ui + Radix UI + Tailwind CSS
- **富文本编辑**：TipTap + 结构化编辑器扩展
- **学习跟踪**：OptimizedScrollTracker（80.4%性能提升）
- **权限系统**：多重管理员检测 + 预览模式
- **数据库**：PostgreSQL (Supabase) + 智能缓存层
- **认证**：自定义身份验证 + 频率限制
- **存储**：Supabase Storage + 媒体管理
- **类型系统**：严格TypeScript + 运行时验证

## 数据库设计（完整实现）

### 核心数据表

#### 优化的学习进度系统
```sql
-- 简化的学习进度表（性能优化版）
CREATE TABLE learning_progress (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  tutorial_id INTEGER REFERENCES tutorials(id),
  progress_percentage INTEGER DEFAULT 0,
  scroll_progress DECIMAL(5,2) DEFAULT 0,
  current_section VARCHAR(100),
  last_accessed TIMESTAMP DEFAULT NOW(),
  device_type VARCHAR(20) DEFAULT 'web',
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学习会话表
CREATE TABLE learning_sessions (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  tutorial_id INTEGER REFERENCES tutorials(id),
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  sections_visited TEXT[],
  total_time INTEGER DEFAULT 0,
  completed_in_session TEXT[],
  session_data JSONB
);

-- 用户成就表
CREATE TABLE user_achievements (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  achievement_type VARCHAR(50) NOT NULL,
  achievement_data JSONB,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  tutorial_id INTEGER REFERENCES tutorials(id)
);
```

#### 内容管理系统（扩展）
```sql
-- 教程表（增强版）
CREATE TABLE tutorials (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT, -- 支持结构化HTML
  category_id INTEGER REFERENCES categories(id),
  tags TEXT[],
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft',
  estimated_time INTEGER DEFAULT 0, -- 预计学习时间
  difficulty_level VARCHAR(20) DEFAULT 'beginner',
  content_type VARCHAR(20) DEFAULT 'html', -- html/structured
  structure_data JSONB, -- 章节结构元数据
  view_count INTEGER DEFAULT 0,
  completion_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 媒体资源表
CREATE TABLE tutorial_media (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER REFERENCES tutorials(id),
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size INTEGER,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  uploaded_by VARCHAR(255)
);
```

#### 公告系统（新增完整模块）
```sql
-- 公告表
CREATE TABLE announcements (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  type VARCHAR(20) DEFAULT 'info',
  target_audience VARCHAR(20) DEFAULT 'all',
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 公告已读记录表
CREATE TABLE announcement_reads (
  id SERIAL PRIMARY KEY,
  announcement_id INTEGER REFERENCES announcements(id),
  user_identifier VARCHAR(255) NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(announcement_id, user_identifier)
);
```

### 关键索引设计（性能优化）
```sql
-- 学习进度优化索引
CREATE INDEX idx_learning_progress_user_tutorial ON learning_progress(user_identifier, tutorial_id);
CREATE INDEX idx_learning_progress_tutorial ON learning_progress(tutorial_id);
CREATE INDEX idx_learning_sessions_user ON learning_sessions(user_identifier);

-- 核心业务索引
CREATE INDEX idx_tutorials_category ON tutorials(category_id);
CREATE INDEX idx_tutorials_status ON tutorials(status);
CREATE INDEX idx_tutorial_keys_code ON tutorial_keys(key_code);
CREATE INDEX idx_user_unlocks_user ON user_unlocks(user_identifier);

-- 公告系统索引
CREATE INDEX idx_announcements_active ON announcements(is_active, created_at);
CREATE INDEX idx_announcement_reads_user ON announcement_reads(user_identifier);
```

## API 设计规范（大幅扩展）

### 学习进度API（新增核心模块）

#### 学习进度跟踪
```typescript
// POST /api/learning/progress
interface LearningProgressRequest {
  tutorialId: number
  status: 'started' | 'in_progress' | 'completed'
  progressPercentage: number
  timeSpent: number
  completedSections: string[]
  currentSection: string
  sessionData?: any
}

interface LearningProgressResponse {
  success: boolean
  data: {
    id: number
    progressPercentage: number
    totalTimeSpent: number
    achievements?: Achievement[]
  }
}
```

#### 学习会话管理
```typescript
// POST /api/learning/sessions
interface SessionRequest {
  tutorialId: number
  action: 'start' | 'update' | 'end'
  sectionId?: string
  timeSpent?: number
  sessionData?: any
}
```

### 增强的教程API
```typescript
// GET /api/tutorial/[id] - 返回增强数据
interface TutorialResponse {
  tutorial: Tutorial
  is_unlocked: boolean
  learning_progress?: LearningProgress
  structure_data?: ChapterSection[]
  estimated_sections?: number
  completion_stats?: {
    total_users: number
    completion_rate: number
    average_time: number
  }
}
```

### 公告系统API（新增）
```typescript
// GET /api/announcements
interface AnnouncementResponse {
  announcements: Announcement[]
  unread_count: number
}

// POST /api/announcements/[id]/read
interface MarkReadRequest {
  user_identifier: string
}
```

## 前端组件设计（重大升级）

### 学习组件架构

#### 核心学习组件
```typescript
// TableOfContents 组件
interface TableOfContentsProps {
  chapters: ChapterSection[]
  currentSection: string
  totalProgress: number
  totalTimeSpent: number
  onSectionClick: (sectionId: string) => void
  onMarkComplete: (sectionId: string) => void
}

// LearningProgress 组件
interface LearningProgressProps {
  tutorialId: number
  userId: string
  onProgressUpdate?: (progress: LearningProgress) => void
  displayMode?: 'compact' | 'detailed' | 'dashboard'
}
```

#### 智能内容解析
```typescript
// learning-utils.ts 核心功能
export interface ChapterSection {
  id: string
  title: string
  estimatedTime: number
  type: 'intro' | 'chapter' | 'checkpoint' | 'interactive' | 'conclusion'
  completed: boolean
  currentlyViewing: boolean
  subsections?: ChapterSection[]
  element?: Element
}

// 智能解析函数
export function parseContentSections(htmlContent: string): ChapterSection[]
export function calculateProgressPercentage(sections: ChapterSection[], completed: string[]): number
export function setupSectionObserver(sections: ChapterSection[], callback: Function): IntersectionObserver
export function estimateReadingTime(content: Element): number
```

### 双编辑器系统（创新设计）

#### 普通编辑器 (TutorialEditor.tsx)
- TipTap 富文本编辑器
- 自动HTML输出
- 基础格式化工具
- 媒体上传支持
- 自动保存机制

#### 结构化编辑器 (StructuredTutorialEditor.tsx)
- 继承普通编辑器所有功能
- 可视化章节属性添加
- 一键插入检查点和互动练习
- 实时结构预览
- 进度跟踪友好的HTML生成

```typescript
// 结构化内容示例
const structuredContent = `
<section data-section="intro" 
         data-section-title="课程介绍" 
         data-estimated-time="5">
  <h1>课程介绍</h1>
  <p>内容...</p>
</section>

<div data-checkpoint="knowledge-check" 
     data-checkpoint-type="knowledge" 
     data-points="10">
  <h4>🎯 知识检查点</h4>
  <p>检查点内容...</p>
</div>
`
```

## 安全设计（增强版）

### 学习数据安全
```typescript
// 学习进度数据加密存储
class SecureLearningStore {
  private encryptData(data: LearningProgress): string {
    // 客户端数据加密
  }
  
  private decryptData(encryptedData: string): LearningProgress {
    // 客户端数据解密
  }
}

// 服务端验证
function validateLearningProgress(data: LearningProgressRequest): boolean {
  // 进度数据合理性验证
  // 防止客户端数据伪造
}
```

### 内容访问控制
```typescript
// 分层访问控制
interface AccessControl {
  tutorial_access: boolean      // 基础教程访问
  progress_tracking: boolean    // 进度跟踪功能
  advanced_features: boolean    // 高级功能
  admin_access: boolean        // 管理权限
}
```

## 性能优化设计（学习功能特化）

### 学习数据缓存策略
```typescript
// 多层缓存设计
interface CacheStrategy {
  level1: 'memory'        // 组件状态缓存（实时）
  level2: 'localStorage'  // 浏览器本地缓存（持久）
  level3: 'supabase'     // 云端数据库（同步）
  sync_interval: 30000   // 30秒同步间隔
}

// 智能缓存更新
class LearningCache {
  // 增量更新策略
  updateProgress(delta: Partial<LearningProgress>): void
  
  // 冲突解决
  resolveConflict(local: LearningProgress, remote: LearningProgress): LearningProgress
  
  // 离线支持
  enableOfflineMode(): void
}
```

### 章节加载优化
```typescript
// 虚拟滚动和懒加载
interface VirtualScrollConfig {
  viewport_height: number
  item_height: number
  buffer_size: number
  lazy_load_threshold: number
}

// 内容预加载策略
class ContentPreloader {
  preloadNextSections(currentIndex: number, count: number): Promise<void>
  prefetchMedia(sectionId: string): Promise<void>
}
```

## 学习分析系统设计（新增核心功能）

### 学习行为数据模型
```typescript
interface LearningAnalytics {
  user_identifier: string
  tutorial_id: number
  learning_patterns: {
    peak_learning_hours: number[]     // 学习高峰时段
    session_duration_avg: number     // 平均会话时长
    completion_speed: number         // 完成速度
    revisit_frequency: number        // 重访频率
  }
  performance_metrics: {
    retention_rate: number           // 知识保持率
    completion_rate: number          // 完成率
    time_efficiency: number          // 学习效率
  }
}
```

### 个性化推荐算法
```typescript
class PersonalizationEngine {
  // 基于学习历史的推荐
  recommendByHistory(userId: string): Tutorial[]
  
  // 基于相似用户的推荐
  recommendBySimilarity(userId: string): Tutorial[]
  
  // 基于学习进度的推荐
  recommendByProgress(userId: string): Tutorial[]
}
```

## 移动端适配设计

### 响应式学习界面
```css
/* 移动端优化 */
@media (max-width: 768px) {
  .learning-layout {
    flex-direction: column;
  }
  
  .table-of-contents {
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .table-of-contents.active {
    transform: translateX(0);
  }
}
```

### 触摸优化
```typescript
// 移动端手势支持
interface TouchGestures {
  swipe_left: 'next_section'
  swipe_right: 'prev_section'
  double_tap: 'mark_complete'
  long_press: 'bookmark'
}
```

## 扩展性设计（面向未来）

### 插件化学习功能
```typescript
interface LearningPlugin {
  name: string
  version: string
  init: (context: LearningContext) => void
  hooks: {
    onSectionEnter?: (section: ChapterSection) => void
    onSectionComplete?: (section: ChapterSection) => void
    onProgressUpdate?: (progress: LearningProgress) => void
  }
}

// 插件注册系统
class LearningPluginManager {
  register(plugin: LearningPlugin): void
  execute(hook: string, data: any): void
}
```

### AI 集成预留接口
```typescript
// AI 学习助手接口
interface AILearningAssistant {
  analyzeProgress(progress: LearningProgress): Promise<LearningInsight>
  generateQuestions(content: string): Promise<Question[]>
  provideFeedback(answers: Answer[]): Promise<Feedback>
  recommendPath(userProfile: UserProfile): Promise<LearningPath>
}
```

### 多平台数据同步
```typescript
// 跨平台同步接口
interface CrossPlatformSync {
  platforms: ('web' | 'mobile' | 'desktop')[]
  sync_strategy: 'real_time' | 'periodic' | 'manual'
  conflict_resolution: 'server_wins' | 'client_wins' | 'merge'
}
```

## 监控和分析设计

### 学习效果监控
```typescript
interface LearningMetrics {
  // 用户维度
  user_engagement: number
  session_quality: number
  completion_prediction: number
  
  // 内容维度  
  content_effectiveness: number
  difficulty_assessment: number
  improvement_suggestions: string[]
  
  // 系统维度
  performance_metrics: {
    load_time: number
    interaction_latency: number
    sync_success_rate: number
  }
}
```

### 实时分析仪表板
```typescript
interface AnalyticsDashboard {
  real_time_learners: number
  completion_rates: Record<string, number>
  popular_sections: ChapterSection[]
  performance_bottlenecks: string[]
  user_feedback: Feedback[]
}
```

这份设计文档反映了系统的最新架构，特别强调了学习进度跟踪系统的创新设计和完整的技术实现方案。  status VARCHAR(20) DEFAULT 'not_started',
  learning_data JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 管理员预览系统
CREATE TABLE admin_sessions (
  id SERIAL PRIMARY KEY,
  session_identifier VARCHAR(255) NOT NULL,
  user_agent TEXT,
  referrer TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP DEFAULT NOW() + INTERVAL '24 hours'
);
```

#### 管理员权限系统（新增）
```sql
-- 管理员预览权限配置
CREATE TABLE admin_permissions (
  id SERIAL PRIMARY KEY,
  permission_type VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  allowed_actions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- 管理员操作日志
CREATE TABLE admin_audit_logs (
  id SERIAL PRIMARY KEY,
  admin_identifier VARCHAR(255),
  action VARCHAR(100),
  resource_type VARCHAR(50),
  resource_id INTEGER,
  metadata JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 用户界面设计（完整规范）

### 设计系统更新

#### 管理员预览主题
```css
/* 管理员预览专用样式 */
.admin-preview-badge {
  @apply bg-purple-100 text-purple-800 border-purple-200 
         px-2 py-1 rounded-md text-sm font-medium
         shadow-sm hover:shadow-md transition-shadow;
}

.admin-preview-banner {
  @apply bg-gradient-to-r from-purple-50 to-indigo-50 
         border-l-4 border-purple-500 p-4 mb-6;
}

.admin-preview-indicator {
  content: "🔓";
  @apply inline-block mr-1;
}
```

#### 学习进度UI优化
```css
/* 学习进度条优化设计 */
.learning-progress-bar {
  @apply h-2 bg-gray-200 rounded-full overflow-hidden
         shadow-inner transition-all duration-300;
}

.learning-progress-fill {
  @apply h-full bg-gradient-to-r from-blue-400 to-blue-600 
         rounded-full transition-all duration-500 ease-out;
}

.completion-celebration {
  @apply flex items-center space-x-2 text-green-600
         font-medium animate-pulse;
}
```

### 响应式设计规范

#### 移动端适配（最新优化）
```yaml
Breakpoints:
  mobile: "max-width: 640px"
  tablet: "641px - 1024px" 
  desktop: "min-width: 1025px"

Mobile Optimizations:
  - 管理员预览标识在移动端自动收缩
  - 学习进度条在小屏幕上保持可见性
  - 触摸友好的交互区域（最小44px）
  - 优化的滚动性能（被动事件监听）
```

#### 无障碍设计标准
```yaml
Accessibility Standards:
  - WCAG 2.1 AA级别合规
  - 键盘导航完全支持
  - 屏幕阅读器兼容性
  - 色彩对比度4.5:1（普通文本）
  - 色彩对比度3:1（大文本）
  - 管理员功能的替代文本支持
```

## 性能优化设计（80.4%提升）

### 学习跟踪性能优化
```typescript
// OptimizedScrollTracker 性能配置
interface OptimizedScrollConfig {
  tutorialId: number
  sections: ChapterSection[]
  updateThrottle: 100      // 100ms节流，平衡性能和实时性
  cacheStrategy: 'memory'  // 内存缓存策略
  persistStrategy: 'debounced' // 防抖持久化
  performanceMode: 'optimized' // 性能优先模式
}

// 性能监控指标
Performance Metrics:
  - CPU使用率降低: 80.4%
  - 内存占用减少: 60.2%
  - 滚动响应延迟: <16ms (60fps)
  - 数据同步频率: 智能调节
```

### 缓存策略设计
```yaml
Cache Layers:
  L1 - 内存缓存: 
    - 学习进度状态 (5分钟)
    - 章节解析结果 (会话期间)
    - 管理员权限状态 (30分钟)
  
  L2 - localStorage:
    - 学习进度备份 (7天)
    - 用户偏好设置 (30天)
    - 管理员会话标识 (24小时)
  
  L3 - 服务端缓存:
    - 教程内容 (1小时)
    - 用户解锁状态 (15分钟)
    - 系统配置 (24小时)
```

## 安全设计规范

### 管理员权限安全
```yaml
管理员身份验证:
  多重检测机制:
    - URL路径检测: /admin 路径识别
    - Referrer检测: 来源页面验证
    - 会话标识: localStorage.admin_session
    - 请求头标识: X-Admin: true

权限验证流程:
  1. 前端身份检测 → 请求头标识
  2. 后端多重验证 → isAdminUser()函数
  3. 权限级别判断 → 预览/编辑/管理权限
  4. 操作日志记录 → 审计追踪

安全边界:
  - 管理员预览仅限内容访问
  - 不绕过实际权限验证
  - 操作日志完整记录
  - 会话自动过期机制
```

### 用户数据保护
```yaml
数据加密:
  - 传输加密: TLS 1.3
  - 敏感数据: AES-256加密存储
  - 密钥管理: Supabase安全密钥轮换

隐私保护:
  - 用户识别: 哈希化处理
  - 学习数据: 最小化收集原则
  - 数据保留: 自动清理策略
  - GDPR合规: 数据删除权支持
```

## 监控和日志设计

### 性能监控
```yaml
实时监控指标:
  - 页面加载时间: <3秒 (目标)
  - API响应时间: <200ms (目标)
  - 学习跟踪延迟: <100ms (目标)
  - 错误率: <0.1% (目标)

监控工具:
  - 前端: Web Vitals + 自定义指标
  - 后端: Supabase Analytics
  - 用户体验: 热力图和录屏分析
```

### 错误追踪
```yaml
错误分类:
  - 系统错误: 500级别服务器错误
  - 业务错误: 权限、验证失败等
  - 用户错误: 输入格式、操作错误
  - 性能错误: 超时、资源不足

错误处理:
  - 优雅降级: 核心功能保持可用
  - 用户反馈: 友好的错误提示
  - 自动恢复: 网络重连、数据同步
  - 开发者工具: 详细的调试信息
```

## 部署和运维设计

### 环境配置
```yaml
开发环境:
  - Next.js开发服务器: localhost:3000
  - Supabase本地实例: 可选
  - 热重载: 启用所有优化功能

生产环境:
  - Vercel部署: 自动优化和CDN
  - Supabase生产实例: 高可用配置
  - 环境变量: 安全的密钥管理
  - 监控告警: 24/7系统监控
```

### 扩展性设计
```yaml
水平扩展:
  - API路由: 无状态设计，易于负载均衡
  - 数据库: Supabase自动扩展
  - 静态资源: CDN分发优化

垂直扩展:
  - 性能优化: 算法和数据结构优化
  - 缓存策略: 多层级缓存设计
  - 代码分割: 按需加载和懒加载
```

---

*本文档最后更新于2025年1月29日，版本v2.1*
*包含最新的管理员预览系统和学习成就优化功能*