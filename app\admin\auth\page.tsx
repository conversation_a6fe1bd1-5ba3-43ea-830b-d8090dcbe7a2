"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, Shield, Lock } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { AnimatedLoader } from "@/components/ui/animated-loader"

export default function AdminLogin() {
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  const { toast } = useToast()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!password.trim()) {
      setError("请输入管理员密码")
      return
    }

    setLoading(true)
    setError("")

    try {
      console.log("🚀 开始登录请求...")
      
      const response = await fetch("/api/admin/auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ password }),
      })

      console.log("📡 登录响应状态:", response.status)
      
      const data = await response.json()
      console.log("📦 登录响应数据:", data)

      if (response.ok && data.success) {
        console.log("✅ 登录成功，开始设置token...")
        
        // 设置认证令牌到 localStorage
        localStorage.setItem("admin_token", data.token)
        console.log("💾 Token已保存到localStorage")
        
        // 检查cookie是否设置成功
        console.log("🍪 检查cookies:", document.cookie)
        
        toast({
          title: "登录成功",
          description: "欢迎使用管理后台",
        })
        
        console.log("🚀 准备跳转到管理后台...")
        console.log("📍 当前路径:", window.location.pathname)
        
        // 先尝试立即跳转
        console.log("⚡ 尝试立即跳转...")
        router.push("/admin")
        
        // 500ms后强制跳转
        setTimeout(() => {
          console.log("🔄 500ms后强制跳转...")
          console.log("📍 跳转前路径:", window.location.pathname)
          window.location.href = "/admin"
        }, 500)
        
        // 1秒后再次检查
        setTimeout(() => {
          console.log("🔍 1秒后检查跳转状态...")
          console.log("📍 当前路径:", window.location.pathname)
          if (window.location.pathname === "/admin/auth") {
            console.log("⚠️ 跳转失败，尝试备用方案...")
            window.location.replace("/admin")
          }
        }, 1000)
        
      } else {
        console.log("❌ 登录失败:", data.error)
        setError(data.error || "登录失败，请检查密码")
      }
    } catch (error) {
      console.error("💥 登录异常:", error)
      setError("网络错误，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto shadow-lg border-slate-200">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-slate-900 rounded-full flex items-center justify-center">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-slate-900">管理员登录</CardTitle>
            <CardDescription className="text-slate-600 mt-2">
              请输入管理员密码以访问后台系统
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-slate-700">
                管理员密码
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="请输入管理员密码"
                  className="pr-10 border-slate-300 focus:border-slate-500"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-slate-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-slate-400" />
                  )}
                </Button>
              </div>
            </div>

            {error && (
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Button 
              type="submit" 
              className="w-full bg-slate-900 hover:bg-slate-800" 
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <AnimatedLoader size="sm" className="mr-2" />
                  验证中...
                </div>
              ) : (
                <div className="flex items-center">
                  <Lock className="h-4 w-4 mr-2" />
                  登录管理后台
                </div>
              )}
            </Button>
          </form>

          <div className="mt-6 pt-4 border-t border-slate-200">
            <div className="text-xs text-slate-500 space-y-1">
              <p>• 管理员密码存储在系统配置中</p>
              <p>• 连续失败5次将锁定账户5分钟</p>
              <p>• 登录会话有效期为1小时</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}