'use client'

import { TutorialEditor } from '@/components/editor/TutorialEditor'
import { useState } from 'react'

export default function TableTestPage() {
  const [content, setContent] = useState('<h1>TipTap表格功能测试</h1><p>请使用工具栏中的表格按钮创建表格，然后测试各种表格操作功能。</p>')

  const handleSave = (content: string) => {
    console.log('保存内容:', content)
    alert('内容已保存到控制台！')
  }

  const handleAutoSave = (content: string) => {
    console.log('自动保存:', content)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
            <h1 className="text-2xl font-bold">🧪 TipTap表格功能测试页面</h1>
            <p className="mt-2 opacity-90">测试所有表格相关功能的完整性和稳定性</p>
          </div>
          
          <div className="p-6">
            <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-800 mb-2">📋 测试清单</h2>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>✅ 1. 点击表格按钮创建基础表格</li>
                <li>✅ 2. 验证光标在表格内时显示表格操作工具栏</li>
                <li>✅ 3. 测试添加行/列功能</li>
                <li>✅ 4. 测试删除行/列功能</li>
                <li>✅ 5. 测试删除整个表格功能</li>
                <li>✅ 6. 在表格单元格中输入各种内容(文本、格式化、链接)</li>
                <li>✅ 7. 测试撤销重做功能</li>
                <li>✅ 8. 测试保存功能</li>
              </ul>
            </div>

            <TutorialEditor
              initialContent={content}
              onSave={handleSave}
              onAutoSave={handleAutoSave}
              className="min-h-[600px]"
              placeholder="请在这里测试表格功能..."
            />
            
            <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-green-800 mb-2">🎯 验收标准</h3>
              <div className="text-sm text-green-700 space-y-2">
                <div><strong>基础功能:</strong> 表格创建、编辑、操作工具栏正常工作</div>
                <div><strong>用户体验:</strong> 操作流畅，界面直观，无卡顿现象</div>
                <div><strong>兼容性:</strong> 与其他编辑器功能协调工作，不影响现有功能</div>
                <div><strong>稳定性:</strong> 大量操作后无崩溃，内存使用合理</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}