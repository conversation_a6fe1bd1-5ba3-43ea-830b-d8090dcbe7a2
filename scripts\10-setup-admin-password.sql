-- 设置管理员密码的 SQL 脚本
-- 默认密码: admin123 (生产环境请务必修改)

-- 删除现有的管理员密码配置（如果存在）
DELETE FROM system_config WHERE config_key = 'admin_password';

-- 插入新的管理员密码哈希
-- 密码: admin123
-- 哈希值是使用 bcrypt 的 12 轮加密生成的
INSERT INTO system_config (config_key, config_value, description, created_at, updated_at)
VALUES (
  'admin_password', 
  '$2a$12$8E9bqn8XgJO5xK2L5VtZ7OnGmL7aGJ9KV3jT4cWdQo.9P2VmfxWJG',
  '管理员登录密码哈希（默认密码: admin123）',
  NOW(),
  NOW()
);

-- 创建管理员操作日志记录
INSERT INTO system_config (config_key, config_value, description, created_at)
VALUES (
  'admin_password_initialized',
  '{"timestamp": "' || NOW() || '", "default_password": true}',
  '管理员密码初始化记录',
  NOW()
);

-- 输出提示信息
SELECT 
  '✅ 管理员密码设置完成' as status,
  'admin123' as default_password,
  '⚠️ 生产环境请立即修改默认密码' as warning;