import { NextRequest, NextResponse } from "next/server"
import { verifyAdmin } from "@/lib/auth"
import jwt from "jsonwebtoken"

// JWT 密钥 - 生产环境请使用环境变量
const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-this-in-production"

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json()

    if (!password) {
      return NextResponse.json(
        { success: false, error: "密码不能为空" },
        { status: 400 }
      )
    }

    // 验证管理员密码
    const isValid = await verifyAdmin(password)
    
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: "密码错误" },
        { status: 401 }
      )
    }

    // 生成 JWT 令牌
    const token = jwt.sign(
      { 
        role: "admin",
        timestamp: Date.now()
      },
      JWT_SECRET,
      { 
        expiresIn: "8h" // 延长到8小时过期
      }
    )

    // 设置安全的 Cookie
    const response = NextResponse.json({
      success: true,
      token,
      message: "登录成功"
    })

    // 设置 HttpOnly Cookie（更安全）
    response.cookies.set("admin_session", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 28800, // 8小时
      path: "/" // 修改路径为根路径，让所有路由都能访问
    })

    return response

  } catch (error) {
    console.error("Admin auth error:", error)
    return NextResponse.json(
      { success: false, error: "服务器错误" },
      { status: 500 }
    )
  }
}

// 验证令牌的函数
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "") ||
                  request.cookies.get("admin_session")?.value

    if (!token) {
      return NextResponse.json(
        { success: false, error: "未提供认证令牌" },
        { status: 401 }
      )
    }

    // 验证 JWT 令牌
    const decoded = jwt.verify(token, JWT_SECRET) as any
    
    if (decoded.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "无效的管理员令牌" },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "令牌有效",
      admin: true
    })

  } catch (error) {
    console.error("Token verification error:", error)
    return NextResponse.json(
      { success: false, error: "令牌无效或已过期" },
      { status: 401 }
    )
  }
}
