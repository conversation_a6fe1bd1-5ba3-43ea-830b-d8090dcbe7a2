"use client"

import { LoadingAnimation } from "@/components/ui/loading-animation"

export default function TestLoadingPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-12">
        <h1 className="text-3xl font-bold text-center mb-8">Loading Animation Tests</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">With Text - Small</h2>
            <LoadingAnimation text="Loading" size="sm" />
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">With Text - Medium</h2>
            <LoadingAnimation text="Loading" size="md" />
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">With Text - Large</h2>
            <LoadingAnimation text="Loading" size="lg" />
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">No Text (Space) - Medium</h2>
            <LoadingAnimation text=" " size="md" />
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Empty Text - Medium</h2>
            <LoadingAnimation text="" size="md" />
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Custom Text - Medium</h2>
            <LoadingAnimation text="正在加载" size="md" />
          </div>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4 text-white">Dark Background Test</h2>
          <LoadingAnimation text=" " size="md" />
        </div>
      </div>
    </div>
  )
}