'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useRouter } from 'next/navigation'
import { 
  CheckCircle, 
  ArrowLeft,
  BookOpen,
  Home,
  Library,
  Settings,
  Eye,
  MousePointer,
  Navigation
} from 'lucide-react'

export default function BackNavigationTestPage() {
  const router = useRouter()
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})

  const markTest = (testId: string, passed: boolean) => {
    setTestResults(prev => ({ ...prev, [testId]: passed }))
  }

  const testScenarios = [
    {
      id: 'my-tutorials-flow',
      title: '我的教程 → 继续学习 → 返回',
      description: '从"我的教程"页面点击"继续学习"，然后点击返回按钮',
      steps: [
        '1. 点击下方"前往我的教程"按钮',
        '2. 在我的教程页面点击任意教程的"继续学习"',
        '3. 在教程页面点击"返回"按钮',
        '4. 应该返回到"我的教程"页面而不是首页'
      ],
      expectedResult: '返回到 /my-tutorials 页面',
      icon: Library,
      status: testResults['my-tutorials-flow']
    },
    {
      id: 'homepage-flow',
      title: '首页 → 查看教程 → 返回',
      description: '从首页直接点击教程，然后点击返回按钮',
      steps: [
        '1. 点击下方"返回首页"按钮',
        '2. 在首页点击任意已解锁教程',
        '3. 在教程页面点击"返回"按钮',
        '4. 应该返回到首页'
      ],
      expectedResult: '返回到首页 /',
      icon: Home,
      status: testResults['homepage-flow']
    },
    {
      id: 'admin-flow',
      title: '管理后台 → 预览教程 → 返回',
      description: '从管理后台预览教程，然后点击返回按钮',
      steps: [
        '1. 点击下方"前往管理后台"按钮',
        '2. 在管理后台点击任意教程的预览按钮',
        '3. 在教程页面点击"返回"按钮',
        '4. 应该返回到管理后台'
      ],
      expectedResult: '返回到 /admin 页面',
      icon: Settings,
      status: testResults['admin-flow']
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Navigation className="h-6 w-6" />
              智能返回导航测试
            </h1>
            <p className="mt-2 opacity-90">验证教程页面返回按钮的智能导航功能</p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* 修复说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  修复说明
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2">✅ 问题已修复</h3>
                  <p className="text-green-700 text-sm">
                    教程详情页面的返回按钮现在会智能检测用户来源，正确返回到相应页面：
                  </p>
                  <ul className="text-green-700 text-sm mt-2 ml-4 space-y-1">
                    <li>• 从"我的教程"进入 → 返回"我的教程"</li>
                    <li>• 从"管理后台"进入 → 返回"管理后台"</li>
                    <li>• 从"首页"进入 → 返回"首页"</li>
                    <li>• 从外部链接进入 → 返回"首页"</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* 快速导航 */}
            <Card>
              <CardHeader>
                <CardTitle>快速导航测试</CardTitle>
                <CardDescription>
                  使用下方按钮快速导航到不同页面进行测试
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => router.push('/my-tutorials')}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <Library className="h-4 w-4" />
                    前往我的教程
                  </Button>
                  <Button 
                    onClick={() => router.push('/')}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <Home className="h-4 w-4" />
                    返回首页
                  </Button>
                  <Button 
                    onClick={() => router.push('/admin')}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <Settings className="h-4 w-4" />
                    前往管理后台
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 测试场景 */}
            <Card>
              <CardHeader>
                <CardTitle>测试场景</CardTitle>
                <CardDescription>
                  按照以下步骤测试不同场景下的返回导航功能
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {testScenarios.map((scenario) => {
                  const Icon = scenario.icon
                  return (
                    <div key={scenario.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start gap-3 mb-3">
                        <Icon className="h-5 w-5 text-gray-600 mt-1" />
                        <div className="flex-1">
                          <h3 className="font-medium">{scenario.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                        </div>
                        {scenario.status !== undefined && (
                          <Badge variant={scenario.status ? "default" : "secondary"}>
                            {scenario.status ? "已测试" : "待测试"}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="bg-gray-50 rounded p-3 mb-3">
                        <h4 className="text-sm font-medium text-gray-800 mb-2">测试步骤：</h4>
                        <ol className="text-sm text-gray-600 space-y-1">
                          {scenario.steps.map((step, index) => (
                            <li key={index}>{step}</li>
                          ))}
                        </ol>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">预期结果：</span>
                          <span className="text-gray-600 ml-1">{scenario.expectedResult}</span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => markTest(scenario.id, true)}
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            通过
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* 技术实现 */}
            <Card>
              <CardHeader>
                <CardTitle>技术实现细节</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium mb-3">智能返回函数逻辑：</h3>
                  <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`const handleSmartBack = () => {
  const referrer = document.referrer
  const currentOrigin = window.location.origin
  
  if (referrer.includes(\`\${currentOrigin}/my-tutorials\`)) {
    router.push('/my-tutorials')  // 返回我的教程
  } else if (referrer.includes(\`\${currentOrigin}/admin\`)) {
    router.push('/admin')  // 返回管理后台
  } else if (referrer.includes(currentOrigin)) {
    router.back()  // 使用浏览器回退
  } else {
    router.push('/')  // 默认返回首页
  }
}`}
                  </pre>
                </div>
              </CardContent>
            </Card>

            {/* 测试结果统计 */}
            <Card>
              <CardHeader>
                <CardTitle>测试结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {Object.values(testResults).filter(Boolean).length}
                    </div>
                    <div className="text-sm text-blue-700">已完成测试</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {Object.values(testResults).filter(Boolean).length}
                    </div>
                    <div className="text-sm text-green-700">测试通过</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-600">
                      {testScenarios.length - Object.values(testResults).filter(Boolean).length}
                    </div>
                    <div className="text-sm text-gray-700">待测试</div>
                  </div>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </div>
  )
}