const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少必要的环境变量');
  console.log('需要设置: NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function updateAnnouncementTypeConstraint() {
  try {
    console.log('🔧 开始更新公告类型约束...');
    
    // 1. 尝试直接插入一个update类型的测试数据来验证是否需要更新约束
    console.log('1. 测试当前约束是否允许 update 类型...');
    const { data: testData, error: testError } = await supabaseAdmin
      .from('announcements')
      .insert({
        title: '测试更新类型公告',
        content: '这是一个测试更新类型的公告',
        type: 'update',
        priority: 50,
        target_audience: 'all'
      })
      .select();
      
    if (testError) {
      console.log('❌ 当前约束不支持 update 类型:', testError.message);
      console.log('');
      console.log('📝 请在 Supabase 控制台的 SQL 编辑器中手动执行以下命令:');
      console.log('');
      console.log('-- 删除旧约束并添加新约束');
      console.log('ALTER TABLE announcements DROP CONSTRAINT IF EXISTS announcements_type_check;');
      console.log('ALTER TABLE announcements ADD CONSTRAINT announcements_type_check CHECK (type IN (\\\'info\\\', \\\'warning\\\', \\\'success\\\', \\\'error\\\', \\\'update\\\'));');
      console.log('');
      console.log('-- 更新注释');
      console.log('COMMENT ON COLUMN announcements.type IS \\\'公告类型：info-信息，warning-警告，success-成功，error-错误，update-更新\\\';');
      console.log('');
      console.log('执行完毕后，重新运行此脚本进行验证。');
      
    } else {
      console.log('✅ 当前约束已支持 update 类型');
      console.log('🧹 清理测试数据...');
      await supabaseAdmin
        .from('announcements')
        .delete()
        .eq('title', '测试更新类型公告');
        
      // 2. 添加示例更新公告
      console.log('2. 添加示例更新公告...');
      const { data: sampleData, error: sampleError } = await supabaseAdmin
        .from('announcements')
        .upsert({
          title: '系统功能更新',
          content: '我们新增了公告管理功能，现在可以创建和管理不同类型的系统通知，包括新的"更新"类型公告。您可以在管理后台创建更新类型的公告来通知用户系统功能的改进和升级。',
          type: 'update',
          priority: 85,
          target_audience: 'all'
        }, {
          onConflict: 'title'
        })
        .select();
        
      if (sampleError) {
        console.error('❌ 添加示例公告失败:', sampleError.message);
      } else {
        console.log('✅ 示例更新公告添加成功');
      }
      
      // 3. 验证更新结果
      console.log('3. 验证更新结果...');
      const { data: announcements, error: fetchError } = await supabaseAdmin
        .from('announcements')
        .select('id, title, type, created_at')
        .eq('type', 'update')
        .order('created_at', { ascending: false });
        
      if (fetchError) {
        console.error('❌ 验证失败:', fetchError.message);
      } else {
        console.log('✅ 验证成功，当前 update 类型公告:');
        if (announcements.length === 0) {
          console.log('  (暂无 update 类型公告)');
        } else {
          announcements.forEach(announcement => {
            console.log(`  - [${announcement.id}] ${announcement.title} (${announcement.type})`);
          });
        }
      }
      
      console.log('🎉 公告类型约束更新完成！现在可以在后台创建 "更新" 类型的公告了。');
    }
    
  } catch (error) {
    console.error('❌ 更新过程中发生异常:', error);
  }
}

updateAnnouncementTypeConstraint();