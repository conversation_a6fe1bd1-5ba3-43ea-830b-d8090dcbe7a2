/**
 * 邮箱魔法链接认证系统
 * 特点：无密码、安全、用户体验佳
 */

import crypto from 'crypto'
import { supabaseAdmin } from './supabase'

/**
 * 生成魔法链接令牌
 * @param email 用户邮箱
 * @returns 签名令牌和链接
 */
export async function generateMagicLink(email: string): Promise<{
  token: string
  magicLink: string
  expiresAt: Date
}> {
  try {
    // 生成随机令牌
    const token = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10分钟有效期
    
    // 创建签名数据
    const signatureData = `${email}:${token}:${expiresAt.getTime()}`
    const signature = crypto
      .createHmac('sha256', process.env.EMAIL_AUTH_SECRET || 'fallback-secret')
      .update(signatureData)
      .digest('hex')
    
    // 保存到数据库
    await supabaseAdmin
      .from('magic_links')
      .insert({
        email,
        token,
        signature,
        expires_at: expiresAt.toISOString(),
        used: false,
        created_at: new Date().toISOString()
      })
    
    // 生成魔法链接
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const magicLink = `${baseUrl}/auth/magic?token=${token}&email=${encodeURIComponent(email)}&sig=${signature}`
    
    console.log('✅ 魔法链接生成成功:', { email, token: token.substring(0, 8) + '...' })
    
    return {
      token,
      magicLink,
      expiresAt
    }
  } catch (error) {
    console.error('❌ 魔法链接生成失败:', error)
    throw new Error('生成登录链接失败')
  }
}

/**
 * 验证魔法链接令牌
 * @param email 用户邮箱
 * @param token 令牌
 * @param signature 签名
 * @returns 验证结果和用户信息
 */
export async function verifyMagicLink(
  email: string, 
  token: string, 
  signature: string
): Promise<{
  valid: boolean
  user?: any
  error?: string
}> {
  try {
    console.log('🔍 开始验证魔法链接:', { email, token: token.substring(0, 8) + '...' })
    
    // 从数据库获取令牌信息
    const { data: linkData, error: fetchError } = await supabaseAdmin
      .from('magic_links')
      .select('*')
      .eq('email', email)
      .eq('token', token)
      .eq('used', false)
      .single()
    
    if (fetchError || !linkData) {
      console.log('❌ 令牌不存在或已使用')
      return { valid: false, error: '登录链接无效或已过期' }
    }
    
    // 检查是否过期
    const expiresAt = new Date(linkData.expires_at)
    if (expiresAt < new Date()) {
      console.log('❌ 令牌已过期')
      return { valid: false, error: '登录链接已过期，请重新获取' }
    }
    
    // 验证签名
    const signatureData = `${email}:${token}:${expiresAt.getTime()}`
    const expectedSignature = crypto
      .createHmac('sha256', process.env.EMAIL_AUTH_SECRET || 'fallback-secret')
      .update(signatureData)
      .digest('hex')
    
    if (signature !== expectedSignature) {
      console.log('❌ 签名验证失败')
      return { valid: false, error: '登录链接签名无效' }
    }
    
    // 标记令牌为已使用
    await supabaseAdmin
      .from('magic_links')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('token', token)
    
    // 获取或创建用户
    let user = await getOrCreateUser(email)
    
    console.log('✅ 魔法链接验证成功:', { email, userId: user.id })
    
    return {
      valid: true,
      user
    }
  } catch (error) {
    console.error('❌ 魔法链接验证异常:', error)
    return { valid: false, error: '验证过程出现错误' }
  }
}

/**
 * 获取或创建用户
 * @param email 用户邮箱
 * @returns 用户信息
 */
async function getOrCreateUser(email: string) {
  try {
    // 先尝试获取现有用户
    const { data: existingUser, error: fetchError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .single()
    
    if (existingUser && !fetchError) {
      console.log('📍 找到现有用户:', { email, userId: existingUser.id })
      
      // 更新最后登录时间
      await supabaseAdmin
        .from('users')
        .update({ 
          last_login_at: new Date().toISOString(),
          login_count: (existingUser.login_count || 0) + 1
        })
        .eq('id', existingUser.id)
      
      return existingUser
    }
    
    // 创建新用户
    const userId = crypto.randomUUID()
    const newUser = {
      id: userId,
      email,
      username: email.split('@')[0], // 使用邮箱前缀作为用户名
      nickname: email.split('@')[0],
      auth_type: 'email',
      email_verified: true, // 通过魔法链接视为已验证
      status: 'active',
      login_count: 1,
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    }
    
    const { data: createdUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert(newUser)
      .select()
      .single()
    
    if (createError) {
      console.error('❌ 用户创建失败:', createError)
      throw new Error('用户创建失败')
    }
    
    console.log('✅ 新用户创建成功:', { email, userId })
    
    return createdUser
  } catch (error) {
    console.error('❌ 获取或创建用户失败:', error)
    throw error
  }
}

/**
 * 发送魔法链接邮件
 * @param email 收件人邮箱
 * @param magicLink 魔法链接
 * @returns 发送结果
 */
export async function sendMagicLinkEmail(
  email: string, 
  magicLink: string
): Promise<boolean> {
  try {
    console.log('📧 准备发送魔法链接邮件:', { email })
    
    // 这里可以集成邮件服务商，如：
    // - 腾讯云邮件
    // - 阿里云邮件
    // - SendGrid
    // - 本地SMTP
    
    // 演示模式：记录到控制台
    console.log(`
╭─────────────────────────────────────╮
│  🪄 知识商城 - 魔法链接登录邮件      │
├─────────────────────────────────────┤
│  收件人: ${email.padEnd(25)}│
│  登录链接: ${magicLink.substring(0, 40)}...│
│  有效期: 10分钟                      │
│  点击链接即可安全登录                 │
╰─────────────────────────────────────╯
    `)
    
    // 实际实现中替换为真实的邮件发送
    // const result = await emailService.send({
    //   to: email,
    //   subject: '知识商城 - 快速登录链接',
    //   html: generateEmailTemplate(magicLink)
    // })
    
    // 模拟发送成功
    return true
  } catch (error) {
    console.error('❌ 魔法链接邮件发送失败:', error)
    return false
  }
}

/**
 * 生成邮件HTML模板
 * @param magicLink 魔法链接
 * @returns HTML邮件内容
 */
function generateEmailTemplate(magicLink: string): string {
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>知识商城 - 快速登录</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
    .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
    .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; 
              text-decoration: none; border-radius: 6px; margin: 20px 0; }
    .footer { text-align: center; font-size: 14px; color: #666; margin-top: 30px; }
    .security { background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🎓 知识商城</div>
      <h1>快速登录链接</h1>
    </div>
    
    <div class="content">
      <p>您好！</p>
      <p>感谢您选择知识商城。点击下方按钮即可快速登录您的账户：</p>
      
      <div style="text-align: center;">
        <a href="${magicLink}" class="button">🪄 立即登录</a>
      </div>
      
      <div class="security">
        <strong>🔒 安全提醒：</strong>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>此链接仅10分钟内有效</li>
          <li>一次性使用，点击后自动失效</li>
          <li>请勿转发此邮件给他人</li>
        </ul>
      </div>
      
      <p>如果按钮无法点击，请复制以下链接到浏览器打开：</p>
      <p style="word-break: break-all; color: #2563eb; font-size: 12px;">${magicLink}</p>
    </div>
    
    <div class="footer">
      <p>如果您没有请求此邮件，请忽略即可。</p>
      <p>© 2025 知识商城 - 让学习更简单</p>
    </div>
  </div>
</body>
</html>
  `
}

/**
 * 清理过期的魔法链接
 * 建议设置定时任务调用此函数
 */
export async function cleanupExpiredMagicLinks(): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .from('magic_links')
      .delete()
      .lt('expires_at', new Date().toISOString())
    
    if (error) {
      console.error('❌ 清理过期链接失败:', error)
      return 0
    }
    
    const deletedCount = Array.isArray(data) ? data.length : 0
    console.log(`🧹 清理了 ${deletedCount} 个过期的魔法链接`)
    
    return deletedCount
  } catch (error) {
    console.error('❌ 清理过期链接异常:', error)
    return 0
  }
}

/**
 * 邮箱格式验证
 * @param email 邮箱地址
 * @returns 是否有效
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 配置常量
 */
export const EMAIL_AUTH_CONFIG = {
  TOKEN_LENGTH: 64, // 令牌长度
  LINK_EXPIRES_MINUTES: 10, // 链接有效期(分钟)
  MAX_ATTEMPTS_PER_HOUR: 5, // 每小时最大请求次数
  CLEANUP_INTERVAL_HOURS: 24 // 清理间隔(小时)
} as const