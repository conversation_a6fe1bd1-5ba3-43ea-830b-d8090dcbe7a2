#!/usr/bin/env node

/**
 * 管理员密码设置工具
 * 用于初始化或更新管理员密码
 */

const bcrypt = require('bcryptjs')
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少必要的环境变量:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function setAdminPassword(password) {
  try {
    console.log('🔐 正在设置管理员密码...')
    
    // 生成密码哈希
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(password, saltRounds)
    
    // 删除现有配置
    await supabase
      .from('system_config')
      .delete()
      .eq('config_key', 'admin_password')
    
    // 插入新的密码哈希
    const { data, error } = await supabase
      .from('system_config')
      .insert({
        config_key: 'admin_password',
        config_value: passwordHash,
        description: '管理员登录密码哈希',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    
    if (error) {
      throw error
    }
    
    // 记录操作日志
    await supabase
      .from('system_config')
      .insert({
        config_key: `admin_password_update_${Date.now()}`,
        config_value: JSON.stringify({
          timestamp: new Date().toISOString(),
          action: 'password_updated'
        }),
        description: '管理员密码更新记录'
      })
    
    console.log('✅ 管理员密码设置成功!')
    console.log(`📝 密码: ${password}`)
    console.log('⚠️  请妥善保管密码，生产环境建议使用强密码')
    
  } catch (error) {
    console.error('❌ 设置密码失败:', error.message)
    process.exit(1)
  }
}

// 获取命令行参数
const args = process.argv.slice(2)
const password = args[0] || 'admin123'

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🔧 管理员密码设置工具

用法:
  node scripts/setup-admin-password.js [密码]
  
参数:
  密码    设置的管理员密码 (默认: admin123)
  
选项:
  -h, --help    显示帮助信息
  
示例:
  node scripts/setup-admin-password.js
  node scripts/setup-admin-password.js mySecurePassword123
  
注意:
  - 生产环境请使用强密码
  - 密码将使用 bcrypt 加密存储
  - 每次运行都会覆盖现有密码
`)
  process.exit(0)
}

// 密码强度检查
if (password.length < 6) {
  console.error('❌ 密码长度至少需要6位字符')
  process.exit(1)
}

console.log('🚀 知识商城 - 管理员密码设置工具')
console.log('='.repeat(40))

setAdminPassword(password)
  .then(() => {
    console.log('='.repeat(40))
    console.log('🎉 设置完成! 现在可以使用新密码登录管理后台')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })