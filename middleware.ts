import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { verifyAdminAuth } from "@/lib/auth-middleware"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  console.log("🔍 中间件处理路径:", pathname)

  // 保护管理员路由
  if (pathname.startsWith("/admin")) {
    // 允许访问登录页面
    if (pathname === "/admin/auth") {
      console.log("✅ 允许访问登录页面")
      return NextResponse.next()
    }

    // 检查管理员认证
    console.log("🔐 开始验证管理员认证...")
    const isAuthenticated = await verifyAdminAuth(request)
    console.log("📋 认证结果:", isAuthenticated ? "通过" : "失败")
    
    if (!isAuthenticated) {
      console.log("❌ 认证失败，重定向到登录页面")
      // 重定向到登录页面
      return NextResponse.redirect(new URL("/admin/auth", request.url))
    } else {
      console.log("✅ 认证通过，允许访问管理后台")
    }
  }

  // 保护管理员 API 路由
  if (pathname.startsWith("/api/admin")) {
    // 允许认证 API
    if (pathname === "/api/admin/auth") {
      console.log("✅ 允许访问认证API")
      return NextResponse.next()
    }

    // 检查管理员认证
    console.log("🔐 开始验证API认证...")
    const isAuthenticated = await verifyAdminAuth(request)
    console.log("📋 API认证结果:", isAuthenticated ? "通过" : "失败")
    
    if (!isAuthenticated) {
      console.log("❌ API认证失败")
      return NextResponse.json(
        { success: false, error: "未授权访问" },
        { status: 401 }
      )
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    "/admin/:path*",
    "/api/admin/:path*"
  ]
}