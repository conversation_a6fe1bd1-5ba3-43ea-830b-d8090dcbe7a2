import { NextRequest, NextResponse } from "next/server"

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-this-in-production"

/**
 * 简化的JWT验证函数（Edge Runtime兼容）
 */
function verifyJWT(token: string): boolean {
  try {
    // 分割JWT token
    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    // 解码payload（不验证签名，仅作临时方案）
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')))
    
    // 检查是否过期
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      console.log("❌ Token已过期")
      return false
    }

    // 检查角色
    if (payload.role !== "admin") {
      console.log("❌ Token角色不是admin:", payload.role)
      return false
    }

    console.log("✅ Token验证通过，角色:", payload.role)
    return true
  } catch (error) {
    console.error("❌ Token解析失败:", error)
    return false
  }
}

/**
 * 管理员认证中间件
 * 保护所有 /admin/* 路由
 */
export async function verifyAdminAuth(request: NextRequest): Promise<boolean> {
  try {
    // 从多个来源获取令牌
    const cookieToken = request.cookies.get("admin_session")?.value
    const authHeader = request.headers.get("authorization")?.replace("Bearer ", "")
    const localStorageToken = request.headers.get("x-admin-token")
    
    const token = cookieToken || authHeader || localStorageToken

    console.log("🔍 Token验证:", {
      cookieToken: cookieToken ? "存在" : "不存在",
      authHeader: authHeader ? "存在" : "不存在", 
      localStorageToken: localStorageToken ? "存在" : "不存在",
      finalToken: token ? "使用token" : "无token"
    })

    if (!token) {
      console.log("❌ 未找到认证令牌")
      return false
    }

    // 使用简化的JWT验证
    const isValid = verifyJWT(token)
    console.log("✅ Token验证结果:", isValid ? "通过" : "失败")
    
    return isValid
  } catch (error) {
    console.error("❌ Admin auth middleware error:", error)
    return false
  }
}

/**
 * API 路由认证中间件
 */
export async function requireAdminAuth(request: NextRequest) {
  const isAuthenticated = await verifyAdminAuth(request)
  
  if (!isAuthenticated) {
    return NextResponse.json(
      { success: false, error: "未授权访问，请先登录" },
      { status: 401 }
    )
  }
  
  return null // 继续处理请求
}