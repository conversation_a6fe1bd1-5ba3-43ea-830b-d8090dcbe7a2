import { Info } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface NavigationHintProps {
  className?: string
}

export function NavigationHint({ className = '' }: NavigationHintProps) {
  return (
    <Alert className={`border-blue-200 bg-blue-50 ${className}`}>
      <Info className="h-4 w-4 text-blue-600" />
      <AlertDescription className="text-blue-800">
        <strong>导航提示：</strong> 
        普通点击在当前页面打开，Ctrl+点击或中键点击在新窗口打开。
      </AlertDescription>
    </Alert>
  )
}