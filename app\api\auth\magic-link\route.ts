import { NextRequest, NextResponse } from "next/server"
import { generateMagicLink, sendMagicLinkEmail, validateEmail } from "@/lib/email-auth"
import { supabaseAdmin } from "@/lib/supabase"

/**
 * 请求魔法链接 API
 * POST /api/auth/magic-link
 */
export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()
    
    // 验证邮箱格式
    if (!email || !validateEmail(email)) {
      return NextResponse.json({
        success: false,
        error: "请输入有效的邮箱地址"
      }, { status: 400 })
    }
    
    console.log('📧 收到魔法链接请求:', { email })
    
    // 检查频率限制
    const clientIP = request.ip || 
                     request.headers.get("x-forwarded-for")?.split(',')[0] || 
                     request.headers.get("x-real-ip") || 
                     "unknown"
    
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
    
    const { data: recentRequests, error: rateLimitError } = await supabaseAdmin
      .from('magic_links')
      .select('id')
      .eq('email', email)
      .gte('created_at', oneHourAgo)
    
    if (rateLimitError) {
      console.error('❌ 频率限制检查失败:', rateLimitError)
      return NextResponse.json({
        success: false,
        error: "系统错误，请稍后重试"
      }, { status: 500 })
    }
    
    if (recentRequests && recentRequests.length >= 5) {
      console.log('⚠️ 频率限制触发:', { email, count: recentRequests.length })
      return NextResponse.json({
        success: false,
        error: "请求过于频繁，请1小时后再试"
      }, { status: 429 })
    }
    
    // 生成魔法链接
    const { magicLink, expiresAt } = await generateMagicLink(email)
    
    // 发送邮件
    const emailSent = await sendMagicLinkEmail(email, magicLink)
    
    if (!emailSent) {
      return NextResponse.json({
        success: false,
        error: "邮件发送失败，请稍后重试"
      }, { status: 500 })
    }
    
    // 记录登录日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        email,
        auth_type: 'email',
        status: 'magic_link_sent',
        ip_address: clientIP,
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 魔法链接发送成功:', { 
      email, 
      expiresAt: expiresAt.toISOString() 
    })
    
    return NextResponse.json({
      success: true,
      message: "登录链接已发送到您的邮箱",
      expiresIn: 10 * 60 * 1000, // 10分钟，毫秒
      // 在开发环境下返回链接用于测试
      ...(process.env.NODE_ENV === 'development' && {
        magicLink, // 仅开发环境返回
        developmentMode: true
      })
    })
    
  } catch (error) {
    console.error('❌ 魔法链接请求失败:', error)
    
    return NextResponse.json({
      success: false,
      error: "请求处理失败，请稍后重试"
    }, { status: 500 })
  }
}

/**
 * 获取邮箱认证状态
 * GET /api/auth/magic-link?email=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const email = searchParams.get('email')
    
    if (!email || !validateEmail(email)) {
      return NextResponse.json({
        success: false,
        error: "请提供有效的邮箱地址"
      }, { status: 400 })
    }
    
    // 检查是否有未使用的有效链接
    const { data: activeLinks, error } = await supabaseAdmin
      .from('magic_links')
      .select('expires_at')
      .eq('email', email)
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
    
    if (error) {
      console.error('❌ 查询魔法链接状态失败:', error)
      return NextResponse.json({
        success: false,
        error: "查询失败"
      }, { status: 500 })
    }
    
    const hasActiveLink = activeLinks && activeLinks.length > 0
    const nextExpiry = hasActiveLink ? activeLinks[0].expires_at : null
    
    // 检查用户是否存在
    const { data: user } = await supabaseAdmin
      .from('users')
      .select('id, email, nickname, created_at')
      .eq('email', email)
      .single()
    
    return NextResponse.json({
      success: true,
      data: {
        email,
        hasActiveLink,
        nextExpiry,
        isRegistered: !!user,
        userInfo: user ? {
          nickname: user.nickname,
          memberSince: user.created_at
        } : null
      }
    })
    
  } catch (error) {
    console.error('❌ 获取认证状态失败:', error)
    
    return NextResponse.json({
      success: false,
      error: "查询失败"
    }, { status: 500 })
  }
}