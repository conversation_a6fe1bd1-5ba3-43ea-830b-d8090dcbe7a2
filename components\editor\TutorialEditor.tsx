'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import { Table, TableRow, TableHeader, TableCell } from '@tiptap/extension-table'
import CharacterCount from '@tiptap/extension-character-count'
import { createLowlight } from 'lowlight'
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import python from 'highlight.js/lib/languages/python'
import java from 'highlight.js/lib/languages/java'
import json from 'highlight.js/lib/languages/json'
import { useState, useCallback, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Bold, 
  Italic, 
  Strikethrough, 
  Code, 
  List, 
  ListOrdered, 
  Quote, 
  Undo, 
  Redo,
  Image as ImageIcon,
  Link as LinkIcon,
  Code2,
  Heading1,
  Heading2,
  Heading3,
  ChevronDown,
  Table as TableIcon,
  Plus,
  Minus,
  Download,
  Upload,
  Rows,
  Columns,
  Trash2
} from 'lucide-react'

// ==========================================
// TipTap富文本编辑器组件
// 支持Markdown、图片、链接、代码块、语法高亮
// ==========================================

// 创建 lowlight 实例并注册语言
const lowlight = createLowlight()
lowlight.register('javascript', javascript)
lowlight.register('typescript', typescript)
lowlight.register('css', css)
lowlight.register('html', html)
lowlight.register('python', python)
lowlight.register('java', java)
lowlight.register('json', json)

interface TutorialEditorProps {
  initialContent?: string
  onSave?: (content: string) => void
  onAutoSave?: (content: string) => void
  mediaUploadUrl?: string
  className?: string
  placeholder?: string
  editable?: boolean
}

export function TutorialEditor({
  initialContent = '',
  onSave,
  onAutoSave,
  mediaUploadUrl = '/api/admin/media/upload',
  className = '',
  placeholder = '开始编写你的教程内容...',
  editable = true
}: TutorialEditorProps) {
  const [isSaving, setIsSaving] = useState(false)
  
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // 禁用默认的代码块
        // 确保列表功能启用
        bulletList: {
          HTMLAttributes: {
            class: 'prose-bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'prose-ordered-list',
          },
        },
        listItem: {
          HTMLAttributes: {
            class: 'prose-list-item',
          },
        },
      }),
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: 'javascript',
      }),
      Table.configure({
        resizable: true,
        cellMinWidth: 100,
        allowTableNodeSelection: false
      }),
      TableRow,
      TableHeader,
      TableCell,
      CharacterCount,
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
    ],
    content: initialContent,
    editable,
    immediatelyRender: false, // 修复SSR水合不匹配问题
    onUpdate: ({ editor }) => {
      const content = editor.getHTML()
      // 自动保存机制
      if (onAutoSave) {
        const autoSaveTimer = setTimeout(() => {
          onAutoSave(content)
        }, 2000) // 2秒后自动保存
        
        return () => clearTimeout(autoSaveTimer)
      }
    },
  })

  // 手动保存
  const handleSave = useCallback(async () => {
    if (!editor || !onSave) return
    
    setIsSaving(true)
    try {
      const content = editor.getHTML()
      await onSave(content)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }, [editor, onSave])

  // 插入图片 - 优化的上传流程
  const insertImage = useCallback(async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.multiple = false
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      // 文件大小限制 (5MB)
      const MAX_SIZE = 5 * 1024 * 1024
      if (file.size > MAX_SIZE) {
        alert('图片文件大小不能超过5MB')
        return
      }

      // 支持的图片格式
      const SUPPORTED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      if (!SUPPORTED_TYPES.includes(file.type)) {
        alert('仅支持 JPG、PNG、GIF、WebP 格式的图片')
        return
      }

      try {
        // 显示上传进度
        const loadingToast = document.createElement('div')
        loadingToast.textContent = '图片上传中...'
        loadingToast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded z-50'
        document.body.appendChild(loadingToast)

        // 创建FormData上传文件
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('/api/admin/media', {
          method: 'POST',
          body: formData
        })

        // 移除加载提示
        document.body.removeChild(loadingToast)

        if (response.ok) {
          const result = await response.json()
          if (result.success && editor) {
            // 插入图片并添加alt属性
            const altText = file.name.replace(/\.[^/.]+$/, '') || '图片'
            editor.chain().focus().setImage({ 
              src: result.data.url,
              alt: altText,
              title: altText
            }).run()
            
            // 成功提示
            const successToast = document.createElement('div')
            successToast.textContent = '图片上传成功'
            successToast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded z-50'
            document.body.appendChild(successToast)
            setTimeout(() => document.body.removeChild(successToast), 2000)
          }
        } else {
          throw new Error('上传失败')
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        
        // 错误提示
        const errorToast = document.createElement('div')
        errorToast.textContent = '图片上传失败，请重试'
        errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded z-50'
        document.body.appendChild(errorToast)
        setTimeout(() => document.body.removeChild(errorToast), 3000)
      }
    }
    
    input.click()
  }, [editor])

  // 插入表格
  const insertTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    }
  }, [editor])

  // 添加行（在当前行之后）
  const addRowAfter = useCallback(() => {
    if (editor) {
      editor.chain().focus().addRowAfter().run()
    }
  }, [editor])

  // 添加列（在当前列之后）
  const addColumnAfter = useCallback(() => {
    if (editor) {
      editor.chain().focus().addColumnAfter().run()
    }
  }, [editor])

  // 删除当前行
  const deleteRow = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteRow().run()
    }
  }, [editor])

  // 删除当前列
  const deleteColumn = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteColumn().run()
    }
  }, [editor])

  // 删除表格
  const deleteTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteTable().run()
    }
  }, [editor])

  // 检查是否在表格中
  const isInTable = editor?.isActive('table') || false

  // 插入链接
  const insertLink = useCallback(() => {
    const url = window.prompt('请输入链接URL:')
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }, [editor])

  // Markdown 导入导出功能
  const exportToMarkdown = useCallback(() => {
    if (!editor) return
    
    const html = editor.getHTML()
    // 简单的HTML到Markdown转换
    let markdown = html
      .replace(/<h1>/g, '# ')
      .replace(/<\/h1>/g, '\n\n')
      .replace(/<h2>/g, '## ')
      .replace(/<\/h2>/g, '\n\n')
      .replace(/<h3>/g, '### ')
      .replace(/<\/h3>/g, '\n\n')
      .replace(/<strong>/g, '**')
      .replace(/<\/strong>/g, '**')
      .replace(/<em>/g, '*')
      .replace(/<\/em>/g, '*')
      .replace(/<code>/g, '`')
      .replace(/<\/code>/g, '`')
      .replace(/<pre><code>/g, '```\n')
      .replace(/<\/code><\/pre>/g, '\n```')
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '\n\n')
      .replace(/<br>/g, '\n')
      .replace(/<ul>/g, '')
      .replace(/<\/ul>/g, '\n')
      .replace(/<ol>/g, '')
      .replace(/<\/ol>/g, '\n')
      .replace(/<li>/g, '- ')
      .replace(/<\/li>/g, '\n')
      .replace(/<blockquote>/g, '> ')
      .replace(/<\/blockquote>/g, '\n\n')
    
    // 创建下载链接
    const blob = new Blob([markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'content.md'
    a.click()
    URL.revokeObjectURL(url)
  }, [editor])

  const importFromMarkdown = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.md,.markdown'
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return
      
      try {
        const text = await file.text()
        
        // 简单的Markdown到HTML转换
        let html = text
          .replace(/^# (.+)$/gm, '<h1>$1</h1>')
          .replace(/^## (.+)$/gm, '<h2>$1</h2>')
          .replace(/^### (.+)$/gm, '<h3>$1</h3>')
          .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.+?)\*/g, '<em>$1</em>')
          .replace(/`(.+?)`/g, '<code>$1</code>')
          .replace(/```\n([\s\S]*?)\n```/g, '<pre><code>$1</code></pre>')
          .replace(/^- (.+)$/gm, '<li>$1</li>')
          .replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')
          .replace(/\n\n/g, '</p><p>')
          .replace(/^\s*$\n/gm, '')
        
        html = '<p>' + html + '</p>'
        
        if (editor) {
          editor.commands.setContent(html)
        }
      } catch (error) {
        console.error('Markdown导入失败:', error)
        alert('Markdown文件导入失败，请检查文件格式')
      }
    }
    
    input.click()
  }, [editor])

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault()
            handleSave()
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleSave])

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-gray-500">编辑器加载中...</div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col min-h-[800px] ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center gap-1 p-2 bg-gray-50 border-b border-gray-200 flex-wrap shadow-sm">
        {/* 基础格式化 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-gray-200' : ''}
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-gray-200' : ''}
        >
          <Italic className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'bg-gray-200' : ''}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={editor.isActive('code') ? 'bg-gray-200' : ''}
        >
          <Code className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 标题 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={editor.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={editor.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}
        >
          <Heading3 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 列表 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
        >
          <List className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 引用和代码块 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
        >
          <Quote className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'bg-gray-200' : ''}
        >
          <Code2 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 媒体插入 */}
        <Button variant="ghost" size="sm" onClick={insertImage}>
          <ImageIcon className="h-4 w-4" />
        </Button>
        
        <Button variant="ghost" size="sm" onClick={insertLink}>
          <LinkIcon className="h-4 w-4" />
        </Button>

        <Button variant="ghost" size="sm" onClick={insertTable}>
          <TableIcon className="h-4 w-4" />
        </Button>

        {/* 表格操作工具栏 - 仅在表格中时显示 */}
        {isInTable && (
          <>
            <Separator orientation="vertical" className="h-6 mx-1" />
            
            {/* 添加行列 */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={addRowAfter}
              title="添加行"
            >
              <Plus className="h-3 w-3" />
              <Rows className="h-3 w-3 ml-1" />
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={addColumnAfter}
              title="添加列"
            >
              <Plus className="h-3 w-3" />
              <Columns className="h-3 w-3 ml-1" />
            </Button>
            
            {/* 删除行列 */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={deleteRow}
              title="删除当前行"
            >
              <Minus className="h-3 w-3" />
              <Rows className="h-3 w-3 ml-1" />
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={deleteColumn}
              title="删除当前列"
            >
              <Minus className="h-3 w-3" />
              <Columns className="h-3 w-3 ml-1" />
            </Button>
            
            {/* 删除表格 */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={deleteTable}
              title="删除表格"
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </>
        )}

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 撤销重做 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>

        {/* 保存按钮 */}
        {onSave && (
          <>
            <Separator orientation="vertical" className="h-6 mx-1" />
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? '保存中...' : '保存 (Ctrl+S)'}
            </Button>
          </>
        )}

        {/* Markdown导入导出 */}
        <Separator orientation="vertical" className="h-6 mx-1" />
        <Button 
          variant="outline" 
          size="sm" 
          onClick={importFromMarkdown}
          title="导入Markdown文件"
        >
          <Upload className="h-4 w-4" />
        </Button>
        
        <Button 
          variant="outline" 
          size="sm" 
          onClick={exportToMarkdown}
          title="导出为Markdown文件"
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>

      {/* 编辑器内容区域 */}
      <div className="w-full flex-1">
        <EditorContent 
          editor={editor} 
          className="min-h-[600px] w-full p-6 focus:outline-none resize-none"
          placeholder={placeholder}
        />
      </div>

      {/* 底部状态栏 */}
      <div className="flex justify-between items-center p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
        <div>
          字符数: {editor.storage.characterCount?.characters() || 0} | 
          单词数: {editor.storage.characterCount?.words() || 0}
        </div>
        <div>
          支持Markdown导入导出 | 表格编辑 | 代码高亮 | Ctrl+S保存
        </div>
      </div>
    </div>
  )
}