/* TipTap编辑器样式优化 */

.ProseMirror {
  outline: none;
  line-height: 1.4;
  color: #374151;
}

.ProseMirror p {
  margin: 0.25rem 0;
}

.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem;
  line-height: 1.2;
  color: #111827;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem;
  line-height: 1.3;
  color: #111827;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem;
  line-height: 1.4;
  color: #111827;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.25rem;
}

.ProseMirror li {
  margin: 0.125rem 0;
  line-height: 1.4;
}

.ProseMirror ul li {
  list-style-type: disc;
}

.ProseMirror ol li {
  list-style-type: decimal;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.ProseMirror code {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror pre code {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ProseMirror a {
  color: #2563eb;
  text-decoration: underline;
  text-decoration-color: #93c5fd;
  transition: all 0.2s ease;
}

.ProseMirror a:hover {
  color: #1d4ed8;
  text-decoration-color: #2563eb;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* 选中状态样式 */
.ProseMirror .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  border-radius: 0.25rem;
}

/* 占位符样式 */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 代码高亮样式 - 更新为支持Lowlight */
.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror pre code {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* Lowlight 语法高亮样式 */
.ProseMirror .hljs-comment {
  color: #6b7280;
}

.ProseMirror .hljs-keyword {
  color: #8b5cf6;
}

.ProseMirror .hljs-string {
  color: #10b981;
}

.ProseMirror .hljs-number {
  color: #f59e0b;
}

.ProseMirror .hljs-function {
  color: #3b82f6;
}

.ProseMirror .hljs-variable {
  color: #ef4444;
}

.ProseMirror .hljs-attr {
  color: #f59e0b;
}

.ProseMirror .hljs-title {
  color: #06b6d4;
}

/* 表格样式 */
.ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  overflow: hidden;
}

.ProseMirror table th,
.ProseMirror table td {
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  text-align: left;
  vertical-align: top;
  position: relative;
}

.ProseMirror table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.ProseMirror table td {
  background-color: #ffffff;
}

.ProseMirror table tr:nth-child(even) td {
  background-color: #f9fafb;
}

.ProseMirror table .selectedCell {
  background-color: #dbeafe;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ProseMirror {
    font-size: 0.875rem;
  }
  
  .ProseMirror h1 {
    font-size: 1.5rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.25rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.125rem;
  }
}