'use client'

interface CubeSpinnerProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function CubeSpinner({ className = '', size = 'md' }: CubeSpinnerProps) {
  const sizeConfig = {
    sm: {
      width: '35.2px',
      height: '35.2px',
      borderWidth: '2px',
      translateZ: '17.6px'
    },
    md: {
      width: '52.8px',
      height: '52.8px', 
      borderWidth: '2.5px',
      translateZ: '26.4px'
    },
    lg: {
      width: '70.4px',
      height: '70.4px',
      borderWidth: '3.5px',
      translateZ: '35.2px'
    }
  }

  const config = sizeConfig[size]

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="cube-spinner">
        <style jsx>{`
          .cube-spinner {
            width: ${config.width};
            height: ${config.height};
            --clr: #409EFF;
            --clr-alpha: rgba(64, 158, 255, 0.1);
            animation: cube-spinner-rotation 1.6s infinite ease;
            transform-style: preserve-3d;
          }

          .cube-spinner > div {
            background-color: var(--clr-alpha);
            height: 100%;
            position: absolute;
            width: 100%;
            border: ${config.borderWidth} solid var(--clr);
          }

          .cube-spinner div:nth-of-type(1) {
            transform: translateZ(-${config.translateZ}) rotateY(180deg);
          }

          .cube-spinner div:nth-of-type(2) {
            transform: rotateY(-270deg) translateX(50%);
            transform-origin: top right;
          }

          .cube-spinner div:nth-of-type(3) {
            transform: rotateY(270deg) translateX(-50%);
            transform-origin: center left;
          }

          .cube-spinner div:nth-of-type(4) {
            transform: rotateX(90deg) translateY(-50%);
            transform-origin: top center;
          }

          .cube-spinner div:nth-of-type(5) {
            transform: rotateX(-90deg) translateY(50%);
            transform-origin: bottom center;
          }

          .cube-spinner div:nth-of-type(6) {
            transform: translateZ(${config.translateZ});
          }

          @keyframes cube-spinner-rotation {
            0% {
              transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
            }
            50% {
              transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
            }
            100% {
              transform: rotate(45deg) rotateX(-385deg) rotateY(385deg);
            }
          }
        `}</style>
        
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
  )
}