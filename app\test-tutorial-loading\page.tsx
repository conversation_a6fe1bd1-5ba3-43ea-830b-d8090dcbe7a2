'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LoadingAnimation } from '@/components/ui/loading-animation'
import { useRouter } from 'next/navigation'
import { 
  <PERSON>rkles, 
  Palette,
  Eye,
  Play,
  ArrowLeft,
  BookOpen,
  Zap
} from 'lucide-react'

export default function TestTutorialLoadingPage() {
  const router = useRouter()
  const [showDemo, setShowDemo] = useState(false)

  const handleTutorialDemo = () => {
    setShowDemo(true)
    // 模拟教程加载时间
    setTimeout(() => {
      setShowDemo(false)
    }, 4000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Sparkles className="h-6 w-6" />
              教程加载动画升级
            </h1>
            <p className="mt-2 opacity-90">精美的"Loading"光波扫过动画替换旧的转圈动画</p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* 对比展示 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5 text-blue-600" />
                  新旧动画对比
                </CardTitle>
                <CardDescription>
                  左侧为旧版转圈动画，右侧为新版光波扫过文字动画
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* 旧版动画 */}
                  <div className="text-center p-8 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">旧版加载动画</h3>
                    <div className="flex flex-col items-center mb-4">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-2"></div>
                      <p className="text-gray-600">加载中...</p>
                    </div>
                    <p className="text-sm text-gray-600">简单的转圈动画 + 静态文字</p>
                  </div>
                  
                  {/* 新版动画 */}
                  <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">新版光波动画</h3>
                    <div className="flex justify-center mb-4">
                      <LoadingAnimation size="md" text="Loading" />
                    </div>
                    <p className="text-sm text-gray-600">多彩光波扫过的动态文字效果</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 尺寸展示 */}
            <Card>
              <CardHeader>
                <CardTitle>不同尺寸展示</CardTitle>
                <CardDescription>
                  新动画支持三种尺寸和自定义文字内容
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">小型 (sm)</h4>
                    <div className="flex justify-center mb-2">
                      <LoadingAnimation size="sm" text="Loading" />
                    </div>
                    <p className="text-xs text-gray-600">适用于组件内加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">中型 (md)</h4>
                    <div className="flex justify-center mb-2">
                      <LoadingAnimation size="md" text="Loading" />
                    </div>
                    <p className="text-xs text-gray-600">适用于页面区域加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">大型 (lg)</h4>
                    <div className="flex justify-center mb-2">
                      <LoadingAnimation size="lg" text="Loading" />
                    </div>
                    <p className="text-xs text-gray-600">适用于全屏加载</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 实际应用场景 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-indigo-600" />
                  实际应用场景
                </CardTitle>
                <CardDescription>
                  在实际使用场景中查看新动画效果
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 教程加载演示 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">教程页面加载演示</h4>
                  <Button onClick={handleTutorialDemo} className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    演示教程加载动画
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">模拟进入教程页面时的加载过程</p>
                </div>

                {/* 真实教程链接 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">查看真实教程加载</h4>
                  <Button 
                    onClick={() => router.push('/tutorial/1')} 
                    variant="outline" 
                    className="flex items-center gap-2"
                  >
                    <BookOpen className="h-4 w-4" />
                    访问教程页面
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">访问真实教程页面体验新的加载动画</p>
                </div>
              </CardContent>
            </Card>

            {/* 技术特性 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  技术特性
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">动画效果</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 光波扫过效果</li>
                      <li>• 逐字母动画显示</li>
                      <li>• 蓝色主题辉光效果</li>
                      <li>• 平滑的透明度变化</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">技术实现</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• CSS mask 遮罩技术</li>
                      <li>• 径向渐变背景</li>
                      <li>• 精确的动画时序</li>
                      <li>• 响应式尺寸设计</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 应用位置 */}
            <Card>
              <CardHeader>
                <CardTitle>已应用位置</CardTitle>
                <CardDescription>
                  新动画已在以下位置替换了旧的转圈动画
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <BookOpen className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium">教程页面加载</div>
                      <div className="text-sm text-gray-600">用户进入教程时的内容加载动画</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 返回按钮 */}
            <div className="flex justify-center pt-4">
              <Button 
                onClick={() => router.push('/')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4" />
                返回主页
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 全屏演示模态框 */}
      {showDemo && (
        <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-auto shadow-lg border-slate-200">
            <CardContent className="flex flex-col items-center py-12">
              <LoadingAnimation size="lg" text="Loading" />
              <p className="text-slate-500 text-center mt-4">模拟教程内容加载...</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}