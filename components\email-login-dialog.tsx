"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail, Send, CheckCircle, Clock, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface EmailLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess: (userInfo: any) => void
}

export function EmailLoginDialog({ open, onOpenChange, onLoginSuccess }: EmailLoginDialogProps) {
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [sent, setSent] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [magicLink, setMagicLink] = useState("") // 开发模式下显示链接
  const { toast } = useToast()

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 发送魔法链接
  const sendMagicLink = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "邮箱格式错误",
        description: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setSent(true)
        setCountdown(60) // 60秒倒计时
        
        // 开发模式下显示魔法链接
        if (data.developmentMode && data.magicLink) {
          setMagicLink(data.magicLink)
        }
        
        // 启动倒计时
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
        
        toast({
          title: "邮件发送成功",
          description: "请查收您的邮箱，点击链接完成登录",
        })
      } else {
        toast({
          title: "发送失败",
          description: data.error || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('发送魔法链接失败:', error)
      toast({
        title: "网络错误",
        description: "请检查网络连接后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 重新发送
  const resendLink = () => {
    setSent(false)
    setMagicLink("")
    sendMagicLink()
  }

  // 重置状态
  const resetForm = () => {
    setEmail("")
    setSent(false)
    setCountdown(0)
    setMagicLink("")
    setLoading(false)
  }

  // 监听对话框关闭
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetForm()
    }
    onOpenChange(isOpen)
  }

  // 直接点击魔法链接（开发模式）
  const handleDirectLogin = async () => {
    if (!magicLink) return
    
    try {
      const response = await fetch(magicLink.replace(window.location.origin, ''))
      const data = await response.json()
      
      if (data.success) {
        onLoginSuccess(data.user)
        onOpenChange(false)
        toast({
          title: "登录成功",
          description: `欢迎 ${data.user.nickname || data.user.username}！`,
        })
      } else {
        toast({
          title: "登录失败",
          description: data.error || "链接可能已过期",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('直接登录失败:', error)
      toast({
        title: "登录异常",
        description: "请重新获取登录链接",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Mail className="h-5 w-5 mr-2 text-blue-600" />
            邮箱快速登录
          </DialogTitle>
          <DialogDescription>
            输入邮箱地址，我们将发送登录链接到您的邮箱
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {!sent ? (
            // 邮箱输入表单
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMagicLink()}
                  disabled={loading}
                />
              </div>
              
              <Button 
                onClick={sendMagicLink} 
                disabled={loading || !email}
                className="w-full"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    发送中...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    发送登录链接
                  </>
                )}
              </Button>
            </div>
          ) : (
            // 发送成功状态
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900">邮件已发送</h3>
                <p className="text-sm text-gray-600 mt-1">
                  登录链接已发送到 <strong>{email}</strong>
                </p>
              </div>

              {/* 开发模式：显示魔法链接 */}
              {magicLink && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3">
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 text-blue-600 mr-2" />
                    <p className="text-sm text-blue-700 font-medium">开发模式</p>
                  </div>
                  <p className="text-xs text-blue-600">
                    点击下方按钮直接登录（生产环境需要查收邮件）
                  </p>
                  <Button 
                    onClick={handleDirectLogin}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    🔗 直接登录（开发模式）
                  </Button>
                </div>
              )}

              <div className="space-y-3">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-center">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">
                      链接10分钟内有效，请及时查收
                    </span>
                  </div>
                </div>

                <Button 
                  onClick={resendLink}
                  variant="outline"
                  disabled={countdown > 0}
                  className="w-full"
                >
                  {countdown > 0 ? (
                    `重新发送 (${countdown}s)`
                  ) : (
                    "重新发送"
                  )}
                </Button>

                <Button 
                  onClick={() => setSent(false)}
                  variant="ghost"
                  className="w-full"
                >
                  使用其他邮箱
                </Button>
              </div>
            </div>
          )}

          {/* 安全提示 */}
          <div className="text-xs text-gray-500 space-y-1 border-t pt-4">
            <p>🔒 安全提示：</p>
            <ul className="space-y-1 ml-4">
              <li>• 登录链接仅10分钟内有效</li>
              <li>• 一次性使用，点击后自动失效</li>
              <li>• 请勿转发登录邮件给他人</li>
              <li>• 首次登录将自动创建账户</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}