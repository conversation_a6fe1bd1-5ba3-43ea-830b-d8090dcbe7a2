import { TutorialEditor } from '@/components/editor/TutorialEditor'

export default function TestEditorPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-8">TipTap 编辑器测试</h1>
      
      <div className="max-w-4xl mx-auto">
        <TutorialEditor
          initialContent="<h1>测试标题</h1><p>这是一个测试段落。</p>"
          placeholder="开始测试编辑器功能..."
          onSave={(content) => {
            console.log('保存内容:', content)
            alert('内容已保存到控制台')
          }}
          onAutoSave={(content) => {
            console.log('自动保存:', content)
          }}
        />
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">测试功能清单:</h2>
        <ul className="space-y-2">
          <li>✅ 编辑区域扩展 (800px 最小高度)</li>
          <li>✅ 行间距优化 (0.5rem 段落间距)</li>
          <li>✅ H1-H3 标题按钮</li>
          <li>✅ 代码语法高亮 (7种语言)</li>
          <li>✅ 字符统计显示</li>
          <li>✅ 图片上传优化</li>
          <li>✅ Markdown 导入导出</li>
          <li>⚠️ 表格功能 (暂时禁用)</li>
        </ul>
      </div>
    </div>
  )
}