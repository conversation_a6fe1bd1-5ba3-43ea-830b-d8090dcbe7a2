import { NextRequest, NextResponse } from "next/server"
import { verifyMagicLink } from "@/lib/email-auth"
import { supabaseAdmin } from "@/lib/supabase"
import crypto from 'crypto'

/**
 * 魔法链接验证 API
 * GET /api/auth/magic?token=xxx&email=xxx&sig=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const token = searchParams.get('token')
    const email = searchParams.get('email')
    const signature = searchParams.get('sig')
    
    if (!token || !email || !signature) {
      return NextResponse.json({
        success: false,
        error: "缺少必要参数"
      }, { status: 400 })
    }
    
    console.log('🔍 魔法链接验证请求:', { 
      email, 
      token: token.substring(0, 8) + '...' 
    })
    
    // 验证魔法链接
    const { valid, user, error } = await verifyMagicLink(email, token, signature)
    
    if (!valid) {
      // 记录失败日志
      await supabaseAdmin
        .from('login_logs')
        .insert({
          email,
          auth_type: 'email',
          status: 'failed',
          error_message: error,
          ip_address: request.ip || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown',
          created_at: new Date().toISOString()
        })
      
      return NextResponse.json({
        success: false,
        error: error || "链接验证失败"
      }, { status: 400 })
    }
    
    // 生成用户会话令牌
    const sessionToken = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
    
    // 保存会话
    await supabaseAdmin
      .from('user_sessions')
      .insert({
        user_id: user.id,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    // 记录成功登录日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: user.id,
        email,
        auth_type: 'email',
        status: 'success',
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 魔法链接登录成功:', { 
      email, 
      userId: user.id,
      sessionToken: sessionToken.substring(0, 8) + '...'
    })
    
    // 返回成功响应和用户信息
    const response = NextResponse.json({
      success: true,
      message: "登录成功",
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        auth_type: user.auth_type,
        email_verified: user.email_verified,
        created_at: user.created_at
      }
    })
    
    // 设置会话Cookie
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 魔法链接验证异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "验证过程出现错误"
    }, { status: 500 })
  }
}