'use client'

interface LoadingAnimationProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  text?: string
}

export function LoadingAnimation({ 
  className = '', 
  size = 'md',
  text = 'Loading'
}: LoadingAnimationProps) {
  const sizeConfig = {
    sm: {
      scale: 1,
      fontSize: '1.2em',
      height: 80,
      width: 120
    },
    md: {
      scale: 1.5,
      fontSize: '1.6em', 
      height: 120,
      width: 180
    },
    lg: {
      scale: 2,
      fontSize: '2em',
      height: 160,
      width: 240
    }
  }

  const config = sizeConfig[size]
  // 如果文本为空或只有空格，则不显示文字，只显示背景动画
  const showText = text.trim().length > 0
  const letters = showText ? text.split('') : []

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes loading-transform-animation {
            0% { transform: translate(-55%); }
            100% { transform: translate(55%); }
          }

          @keyframes loading-opacity-animation {
            0%, 100% { opacity: 0; }
            15% { opacity: 1; }
            65% { opacity: 0; }
          }

          @keyframes loading-letter-anim {
            0% { opacity: 0; }
            5% {
              opacity: 1;
              text-shadow: 0 0 8px #3b82f6, 0 0 16px #3b82f6;
              transform: scale(1.1) translateY(-2px);
            }
            20% { opacity: 0.2; }
            100% { opacity: 0; }
          }

          .loading-animation-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2rem;
            font-family: "system-ui", -apple-system, "Segoe UI", sans-serif;
            font-weight: 600;
            user-select: none;
            color: #1f2937;
          }

          .loading-animation-loader {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            z-index: 1;
            background-color: transparent;
            mask: repeating-linear-gradient(90deg, transparent 0, transparent 6px, black 7px, black 8px);
            -webkit-mask: repeating-linear-gradient(90deg, transparent 0, transparent 6px, black 7px, black 8px);
          }

          .loading-animation-loader::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
              radial-gradient(circle at 50% 50%, #ff0 0%, transparent 50%),
              radial-gradient(circle at 45% 45%, #f00 0%, transparent 45%),
              radial-gradient(circle at 55% 55%, #0ff 0%, transparent 45%),
              radial-gradient(circle at 45% 55%, #0f0 0%, transparent 45%),
              radial-gradient(circle at 55% 45%, #00f 0%, transparent 45%);
            animation: 
              loading-transform-animation 2s infinite alternate,
              loading-opacity-animation 4s infinite;
            animation-timing-function: cubic-bezier(0.6, 0.8, 0.5, 1);
          }

          .loading-animation-loader.no-text::after {
            mask: radial-gradient(circle at 50% 50%, black 0%, black 70%, transparent 85%);
            -webkit-mask: radial-gradient(circle at 50% 50%, black 0%, black 70%, transparent 85%);
          }

          .loading-animation-loader.with-text::after {
            mask: radial-gradient(circle at 50% 50%, transparent 0%, transparent 10%, black 25%);
            -webkit-mask: radial-gradient(circle at 50% 50%, transparent 0%, transparent 10%, black 25%);
          }

          .loading-letter {
            display: inline-block;
            opacity: 0;
            animation: loading-letter-anim 4s infinite linear;
            z-index: 2;
            position: relative;
          }

          .loading-letter:nth-child(1) { animation-delay: 0.1s; }
          .loading-letter:nth-child(2) { animation-delay: 0.205s; }
          .loading-letter:nth-child(3) { animation-delay: 0.31s; }
          .loading-letter:nth-child(4) { animation-delay: 0.415s; }
          .loading-letter:nth-child(5) { animation-delay: 0.521s; }
          .loading-letter:nth-child(6) { animation-delay: 0.626s; }
          .loading-letter:nth-child(7) { animation-delay: 0.731s; }
          .loading-letter:nth-child(8) { animation-delay: 0.836s; }
        `
      }} />
      
      <div className={`flex items-center justify-center ${className}`}>
        <div 
          className="loading-animation-wrapper"
          style={{
            height: `${config.height}px`,
            width: showText ? 'auto' : `${config.width}px`,
            minWidth: `${config.width}px`,
            fontSize: config.fontSize,
            transform: `scale(${config.scale})`,
          }}
        >        
          {showText && letters.map((letter, index) => (
            <span key={index} className="loading-letter">
              {letter}
            </span>
          ))}
          
          <div className={`loading-animation-loader ${showText ? 'with-text' : 'no-text'}`}></div>
        </div>
      </div>
    </>
  )
}