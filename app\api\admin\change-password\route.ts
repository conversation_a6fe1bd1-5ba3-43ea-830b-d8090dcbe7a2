import { NextRequest, NextResponse } from "next/server"
import { verifyAdmin, updateAdminPassword } from "@/lib/auth"

export async function POST(request: NextRequest) {
  try {
    const { currentPassword, newPassword } = await request.json()

    // 验证输入
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { success: false, error: "当前密码和新密码都不能为空" },
        { status: 400 }
      )
    }

    if (newPassword.length < 6) {
      return NextResponse.json(
        { success: false, error: "新密码长度至少需要6位字符" },
        { status: 400 }
      )
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyAdmin(currentPassword)
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { success: false, error: "当前密码错误" },
        { status: 401 }
      )
    }

    // 更新密码
    const updateSuccess = await updateAdminPassword(newPassword)
    
    if (!updateSuccess) {
      return NextResponse.json(
        { success: false, error: "密码更新失败，请稍后重试" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "密码修改成功，请重新登录"
    })

  } catch (error) {
    console.error("Change password error:", error)
    return NextResponse.json(
      { success: false, error: "服务器错误" },
      { status: 500 }
    )
  }
}